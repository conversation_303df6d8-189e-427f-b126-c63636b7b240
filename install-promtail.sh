#!/bin/bash

source .env

# Prompt user for app type (default: php)
read -p "Enter app type [php]: " APP_TYPE
APP_TYPE=${APP_TYPE:-php}

# Prompt user for namespace (default: main)
read -p "Enter namespace [main]: " NAMESPACE
NAMESPACE=${NAMESPACE:-main}

# Prompt user for server architecture (default: amd64)
echo "Select server architecture:"
echo "1) amd64 (default)"
echo "2) arm64"
read -p "Enter choice [1]: " ARCH_CHOICE
ARCH_CHOICE=${ARCH_CHOICE:-1}

if [ "$ARCH_CHOICE" = "2" ]; then
    ARCHITECTURE="arm64"
    PROMTAIL_ZIP_URL="https://github.com/grafana/loki/releases/latest/download/promtail-linux-arm64.zip"
    PROMTAIL_ZIP_FILE="promtail-linux-arm64.zip"
else
    ARCHITECTURE="amd64"
    PROMTAIL_ZIP_URL="https://github.com/grafana/loki/releases/download/v3.2.0/promtail-linux-amd64.zip"
    PROMTAIL_ZIP_FILE="promtail-linux-amd64.zip"
fi

echo "Configuration:"
echo "  App type: $APP_TYPE"
echo "  Namespace: $NAMESPACE"
echo "  Architecture: $ARCHITECTURE"
echo ""

APP_DOMAIN=$(echo $APP_URL | awk -F[/:] '{print $4}')
LOG_DIR="/home/<USER>/$APP_DOMAIN/storage/logs"
PROMTAIL_DIR="/home/<USER>/$APP_DOMAIN/promtail"
CONFIG_FILE="$PROMTAIL_DIR/config.yaml"
PROMTAIL_BINARY="$PROMTAIL_DIR/promtail"
PROMTAIL_ZIP="$PROMTAIL_DIR/$PROMTAIL_ZIP_FILE"

if [ ! -d "$PROMTAIL_DIR" ]; then
    echo "Promtail directory not found. Creating $PROMTAIL_DIR..."
    mkdir -p "$PROMTAIL_DIR"
fi

if [ ! -f "$PROMTAIL_BINARY" ]; then
    echo "Promtail binary not found. Downloading and unzipping..."
    curl -L -o "$PROMTAIL_ZIP" "$PROMTAIL_ZIP_URL"
    unzip -o "$PROMTAIL_ZIP" -d "$PROMTAIL_DIR"
    chmod +x "$PROMTAIL_BINARY"
    rm "$PROMTAIL_ZIP"
    echo "Promtail has been set up at $PROMTAIL_BINARY."
else
    echo "Promtail binary already exists at $PROMTAIL_BINARY."
fi

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Config file not found. Creating config.yaml..."

    mkdir -p "$PROMTAIL_DIR"

    cat <<EOL > "$CONFIG_FILE"
server:
  http_listen_port: 0
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: $LOKI_URL
    basic_auth:
      username: $LOKI_USERNAME
      password: $LOKI_PASSWORD

scrape_configs:
  - job_name: laravel
    static_configs:
      - targets:
          - localhost
        labels:
          job: laravel
          __path__: $LOG_DIR/laravel.log
          env: $APP_ENV
          namespace: $NAMESPACE
          app: $APP_TYPE
EOL
    echo "config.yaml has been created at $CONFIG_FILE."
else
    echo "config.yaml already exists at $CONFIG_FILE."
fi
