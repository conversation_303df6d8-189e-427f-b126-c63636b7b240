<?php

declare(strict_types=1);

namespace Tests\Unit\Enquiry\LeadAutomation;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\Enquiry\LeadAutomation\Actions\AutoAssign;
use App\Enquiry\LeadAutomation\NewLeadAutomationExecutor;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Tests\LogEntry;
use Tests\LogFake;
use Tests\TestCase;

final class NewLeadAutomationExecutorTest extends TestCase
{
    use DatabaseTransactions;

    private NewLeadAutomationExecutor $executor;

    private AutoAssign&MockInterface $autoAssign;

    protected function setUp(): void
    {
        parent::setUp();
        $this->autoAssign = Mockery::mock(AutoAssign::class);
        $this->executor = new NewLeadAutomationExecutor($this->autoAssign);

        Log::swap(new LogFake());
    }

    /**
     * @test
     */
    public function it_should_skip_when_enquiry_not_found(): void
    {
        $enquiryId = 999;
        $vendorId = 1001;

        $this->executor->for(enquiryId: $enquiryId, vendorId: $vendorId);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Unable to find enquiry for lead automation'
                && $log->context === [
                    'enquiry_id' => $enquiryId,
                    'vendor_id' => $vendorId,
                ]
        );
    }

    /**
     * @test
     */
    public function it_should_skip_when_no_rules_found(): void
    {
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111001,
            'fk_int_user_id' => 1000,
            'created_by' => 1,
            'fk_int_enquiry_type_id' => 1,
            'fk_int_purpose_id' => null,
        ]);

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'No rules found for lead automation'
                && $log->context === [
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'vendor_id' => $enquiry->fk_int_user_id,
                    'source_id' => $enquiry->fk_int_enquiry_type_id,
                    'purpose_id' => $enquiry->fk_int_purpose_id,
                ]
        );
    }

    /**
     * @test
     */
    public function it_should_auto_assign_agent_when_no_staff_assigned(): void
    {
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111,
            'fk_int_user_id' => 1000,
            'created_by' => 1,
            'fk_int_enquiry_type_id' => 1,
            'fk_int_purpose_id' => null,
        ]);

        $rule = AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_source_id' => $enquiry->fk_int_enquiry_type_id,
            'enquiry_purpose_id' => null,
        ]);

        $this->autoAssign->shouldReceive('with')
            ->once()
            ->with(
                Mockery::on(static fn ($arg) => $arg->id === $rule->id),
                Mockery::on(static fn ($arg) => $arg->pk_int_enquiry_id === $enquiry->pk_int_enquiry_id)
            );

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Lead automation executed'
                && $log->context === [
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'vendor_id' => $enquiry->fk_int_user_id,
                    'source_id' => $enquiry->fk_int_enquiry_type_id,
                    'purpose_id' => $enquiry->fk_int_purpose_id,
                ]
        );
    }

    /**
     * @test
     */
    public function it_should_use_fallback_rule_when_source_specific_rule_not_found(): void
    {
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111,
            'fk_int_user_id' => 1000,
            'created_by' => 1,
            'fk_int_enquiry_type_id' => 1,
            'fk_int_purpose_id' => null,
        ]);

        $fallbackRule = AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_source_id' => null,
            'enquiry_purpose_id' => null,
        ]);

        $this->autoAssign->shouldReceive('with')
            ->once()
            ->with(
                Mockery::on(static fn ($arg) => $arg->id === $fallbackRule->id),
                Mockery::on(static fn ($arg) => $arg->pk_int_enquiry_id === $enquiry->pk_int_enquiry_id)
            );

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);
    }

    /**
     * @test
     */
    public function it_should_skip_auto_assignment_when_staff_already_assigned(): void
    {
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111,
            'fk_int_user_id' => 1000,
            'staff_id' => 1,
            'fk_int_enquiry_type_id' => 1,
            'fk_int_purpose_id' => null,
        ]);

        AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_purpose_id' => null,
        ]);

        $this->autoAssign->shouldNotReceive('with');

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);

        Log::assertLogged(
            static fn (LogEntry $log): bool => $log->level === 'info'
                && $log->message === 'Lead automation executed'
                && $log->context === [
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'vendor_id' => $enquiry->fk_int_user_id,
                    'source_id' => $enquiry->fk_int_enquiry_type_id,
                    'purpose_id' => $enquiry->fk_int_purpose_id,
                ]
        );
    }

    /**
     * @test
     */
    public function it_should_filter_rules_by_purpose_id(): void
    {
        $purposeId = 5;
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111,
            'fk_int_user_id' => 1000,
            'fk_int_purpose_id' => $purposeId,
            'created_by' => 1,
        ]);

        // Create rule with matching purpose_id
        $matchingRule = AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_source_id' => $enquiry->fk_int_enquiry_type_id,
            'enquiry_purpose_id' => $purposeId,
        ]);

        // Create rule with different purpose_id (should not match)
        AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_source_id' => $enquiry->fk_int_enquiry_type_id,
            'enquiry_purpose_id' => 999, // Different purpose
        ]);

        $this->autoAssign->shouldReceive('with')
            ->once()
            ->with(
                Mockery::on(static fn ($arg) => $arg->id === $matchingRule->id),
                Mockery::on(static fn ($arg) => $arg->pk_int_enquiry_id === $enquiry->pk_int_enquiry_id)
            );

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);
    }

    /**
     * @test
     */
    public function it_should_match_rules_with_null_purpose_id(): void
    {
        $purposeId = 5;
        $enquiry = Enquiry::create([
            'pk_int_enquiry_id' => 10001111,
            'fk_int_user_id' => 1000,
            'fk_int_purpose_id' => $purposeId,
            'created_by' => 1,
        ]);

        // Create rule with null purpose_id (should match any purpose)
        $nullPurposeRule = AutomationRule::factory()->create([
            'vendor_id' => $enquiry->fk_int_user_id,
            'trigger' => 'new_lead',
            'action' => 'assign',
            'enquiry_source_id' => $enquiry->fk_int_enquiry_type_id,
            'enquiry_purpose_id' => null,
        ]);

        $this->autoAssign->shouldReceive('with')
            ->once()
            ->with(
                Mockery::on(static fn ($arg) => $arg->id === $nullPurposeRule->id),
                Mockery::on(static fn ($arg) => $arg->pk_int_enquiry_id === $enquiry->pk_int_enquiry_id)
            );

        $this->executor->for(enquiryId: $enquiry->pk_int_enquiry_id, vendorId: $enquiry->fk_int_user_id);
    }
}
