<?php

declare(strict_types=1);

namespace Tests\Unit\GlApi\Http\Requests;

use App\GlApi\Http\Requests\CreateNoteRequest;
use ReflectionClass;
use Shared\Exceptions\PhoneNumberIsInvalid;
use Shared\ValueObjects\PhoneNumber;
use Tests\TestCase;

class CreateNoteRequestTest extends TestCase
{
    /**
     * @test
     */
    public function it_has_correct_validation_rules(): void
    {
        $request = new CreateNoteRequest();
        $rules = $request->rules();

        $expectedRules = [
            'note' => ['required', 'string', 'max:2000'],
            'mobileno' => ['nullable', 'string', 'required_without:email'],
            'countrycode' => ['nullable', 'string', 'max:5'],
            'email' => ['nullable', 'email', 'max:255', 'required_without:mobileno'],
        ];

        $this->assertEquals($expectedRules, $rules);
    }

    /**
     * @test
     */
    public function it_has_correct_validation_messages(): void
    {
        $request = new CreateNoteRequest();
        $messages = $request->messages();

        $expectedMessages = [
            'note.required' => 'Note content is required',
            'note.max' => 'Note cannot exceed 2000 characters',
            'mobileno.numeric' => 'Mobile number must be numeric',
            'mobileno.digits_between' => 'Mobile number must be between 8 and 14 digits',
            'mobileno.required_without' => 'Either mobile number or email is required',
            'countrycode.required_with' => 'Country code is required when mobile number is provided',
            'email.email' => 'Please provide a valid email address',
            'email.required_without' => 'Either email or mobile number is required',
        ];

        $this->assertEquals($expectedMessages, $messages);
    }

    // ===== FIELD NORMALIZATION TESTS =====

    /**
     * @test
     */
    public function it_normalizes_mobile_no_to_mobileno(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobile_no' => '9876543210',
        ]);

        // Access the mobileno field to trigger normalization
        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_prefers_mobileno_over_mobile_no_when_both_exist(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '9876543210',
            'mobile_no' => '1234567890',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        // Should use mobileno (9876543210), not mobile_no (1234567890)
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_handles_mobile_no_with_country_code(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobile_no' => '2025551234',
            'countrycode' => '1',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+12025551234', $phoneNumber->toE164PhoneNumber());
    }

    // ===== PHONE NUMBER PARSING TESTS =====

    /**
     * @test
     */
    public function parse_phone_number_returns_null_when_mobileno_is_missing(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'email' => '<EMAIL>',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function parse_phone_number_returns_null_when_mobileno_is_empty(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '',
            'email' => '<EMAIL>',
        ]);

        $this->assertNull($request->parsePhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_returns_null_when_mobileno_is_whitespace(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '   ',
            'email' => '<EMAIL>',
        ]);

        $this->assertNull($request->parsePhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_handles_valid_indian_number_with_plus_prefix(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '+************',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_handles_valid_indian_number_without_plus_prefix(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '************',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_uses_country_code_when_mobile_number_is_invalid(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '9876543210',
            'countrycode' => '91',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_uses_default_country_code_91_when_country_code_is_null(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '9876543210',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_removes_plus_from_mobile_number_when_combining_with_country_code(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '+9876543210',
            'countrycode' => '91',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_uses_different_country_codes(): void
    {
        // Test with US country code and a valid US number
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '2025551234',
            'countrycode' => '1',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+12025551234', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_throws_exception_when_all_attempts_fail(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '123abc456', // This will become '123456' after cleaning
            'countrycode' => 'invalid', // This will remain 'invalid'
        ]);

        $this->expectException(PhoneNumberIsInvalid::class);

        $request->parsePhoneNumber();
    }

    /**
     * @test
     */
    public function parse_phone_number_handles_edge_case_with_zero(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '0',
        ]);

        $this->expectException(PhoneNumberIsInvalid::class);
        $request->parsePhoneNumber();
    }

    /**
     * @test
     */
    public function parse_phone_number_handles_short_indian_number_with_country_code(): void
    {
        // Test 10-digit Indian number without country code
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '9876543210',
            'countrycode' => '91',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function parse_phone_number_handles_uk_number(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '+************',
        ]);

        $result = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $result);
        $this->assertEquals('+************', $result->toE164PhoneNumber());
    }

    // ===== MOBILE NUMBER CLEANING TESTS =====

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_spaces(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '98 765 432 10',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_dashes(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '************',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_parentheses_and_spaces(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '(*************',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_dots(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '************',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_preserves_leading_plus_when_cleaning(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '+91 ************',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_no_field_with_special_characters(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobile_no' => '(+91) ************',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_letters(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '987abc654def3210',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_handles_mobile_number_with_multiple_plus_signs(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '+91+987+654+3210',
        ]);

        $phoneNumber = $request->parsePhoneNumber();

        $this->assertInstanceOf(PhoneNumber::class, $phoneNumber);
        $this->assertEquals('+************', $phoneNumber->toE164PhoneNumber());
    }

    /**
     * @test
     */
    public function it_cleans_mobile_number_with_special_unicode_characters(): void
    {
        $request = $this->createRequest([
            'note' => 'Test note',
            'mobileno' => '９８７－６５４－３２１０', // Full-width numbers and hyphens
        ]);

        // Since full-width unicode numbers are not standard digits, they'll be removed
        // resulting in an empty string which will return null
        $phoneNumber = $request->parsePhoneNumber();

        $this->assertNull($phoneNumber);
    }

    private function createRequest(array $data = []): CreateNoteRequest
    {
        $request = new CreateNoteRequest();
        $request->replace($data);

        // Manually trigger prepareForValidation to simulate Laravel's behavior
        $reflection = new ReflectionClass($request);
        $method = $reflection->getMethod('prepareForValidation');
        $method->setAccessible(true);
        $method->invoke($request);

        return $request;
    }
}
