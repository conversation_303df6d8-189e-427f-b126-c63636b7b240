<?php

declare(strict_types=1);

namespace Tests\Unit\Common\Filters;

use App\Common\Filters\DateRangeFilter;
use App\Task;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Tests\TestCase;

final class DateRangeFilterTest extends TestCase
{
    /**
     * @test
     */
    public function it_filters_by_today(): void
    {
        $filter = new DateRangeFilter('today');
        $query = Task::query();
        
        $result = $filter($query);
        
        $this->assertInstanceOf(Builder::class, $result);
        
        // Check that the query has the correct where conditions
        $bindings = $result->getBindings();
        $this->assertCount(2, $bindings);
        
        // Verify the dates are for today
        $this->assertEquals(Carbon::today()->startOfDay()->toDateTimeString(), $bindings[0]);
        $this->assertEquals(Carbon::today()->endOfDay()->toDateTimeString(), $bindings[1]);
    }

    /**
     * @test
     */
    public function it_filters_by_yesterday(): void
    {
        $filter = new DateRangeFilter('yesterday');
        $query = Task::query();
        
        $result = $filter($query);
        
        $bindings = $result->getBindings();
        $this->assertCount(2, $bindings);
        
        // Verify the dates are for yesterday
        $this->assertEquals(Carbon::yesterday()->startOfDay()->toDateTimeString(), $bindings[0]);
        $this->assertEquals(Carbon::yesterday()->endOfDay()->toDateTimeString(), $bindings[1]);
    }

    /**
     * @test
     */
    public function it_filters_by_this_week(): void
    {
        $filter = new DateRangeFilter('this_week');
        $query = Task::query();
        
        $result = $filter($query);
        
        $bindings = $result->getBindings();
        $this->assertCount(2, $bindings);
        
        // Verify the dates are for this week
        $this->assertEquals(Carbon::today()->startOfWeek()->toDateTimeString(), $bindings[0]);
        $this->assertEquals(Carbon::today()->endOfWeek()->toDateTimeString(), $bindings[1]);
    }

    /**
     * @test
     */
    public function it_filters_by_custom_date_range(): void
    {
        $startDate = Carbon::parse('2023-01-01');
        $endDate = Carbon::parse('2023-01-31');
        
        $filter = new DateRangeFilter('custom', $startDate, $endDate);
        $query = Task::query();
        
        $result = $filter($query);
        
        $bindings = $result->getBindings();
        $this->assertCount(2, $bindings);
        
        // Verify the custom dates
        $this->assertEquals($startDate->toDateTimeString(), $bindings[0]);
        $this->assertEquals($endDate->toDateTimeString(), $bindings[1]);
    }

    /**
     * @test
     */
    public function it_uses_custom_date_column(): void
    {
        $filter = new DateRangeFilter('today', null, null, 'updated_at');
        $query = Task::query();
        
        $result = $filter($query);
        
        // Check that the query uses the custom column
        $sql = $result->toSql();
        $this->assertStringContainsString('`tasks`.`updated_at`', $sql);
    }

    /**
     * @test
     */
    public function it_returns_unmodified_query_when_no_filter(): void
    {
        $filter = new DateRangeFilter();
        $query = Task::query();
        $originalSql = $query->toSql();
        
        $result = $filter($query);
        
        // Query should remain unchanged
        $this->assertEquals($originalSql, $result->toSql());
    }

    /**
     * @test
     */
    public function it_handles_last_30_days_filter(): void
    {
        $filter = new DateRangeFilter('last_30_days');
        $query = Task::query();
        
        $result = $filter($query);
        
        $bindings = $result->getBindings();
        $this->assertCount(2, $bindings);
        
        // Verify the dates are for last 30 days
        $this->assertEquals(Carbon::today()->subDays(30)->toDateTimeString(), $bindings[0]);
        $this->assertEquals(Carbon::today()->toDateTimeString(), $bindings[1]);
    }
}
