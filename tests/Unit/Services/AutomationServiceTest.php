<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\BackendModel\Enquiry;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AssignAgent;
use App\Services\AutomationService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Collection;
use Mockery;
use Mockery\MockInterface;
use ReflectionClass;
use Tests\TestCase;

final class AutomationServiceTest extends TestCase
{
    use DatabaseTransactions;

    private AutomationService $service;
    private MockInterface $assignAgentMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the AssignAgent class
        $this->assignAgentMock = Mockery::mock('overload:' . AssignAgent::class);
        
        $this->service = new AutomationService();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_skips_when_staff_already_assigned(): void
    {
        // Create a mock Enquiry object
        $enquiry = $this->createMockEnquiry(staffId: 123, sourceId: 5, purposeId: 10);

        $automationRules = new Collection();

        // AssignAgent should NOT be called when staff is already assigned
        $this->assignAgentMock->shouldNotReceive('byRule');

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_filters_by_source_and_purpose(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        // Create mock rules
        $matchingRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: 10
        );

        $nonMatchingRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: 20 // Different purpose
        );

        $automationRules = new Collection([$matchingRule, $nonMatchingRule]);

        // AssignAgent should be called with the matching rule
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($matchingRule) {
                    return $rule === $matchingRule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_uses_null_purpose_rule_as_wildcard(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        $wildcardRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: null // Matches any purpose
        );

        $automationRules = new Collection([$wildcardRule]);

        // AssignAgent should be called with the wildcard rule
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($wildcardRule) {
                    return $rule === $wildcardRule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_prefers_source_specific_over_fallback(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        $sourceSpecificRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: null
        );

        $fallbackRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: null,
            purposeId: null
        );

        // Order doesn't matter anymore since we sort by source specificity
        $automationRules = new Collection([$fallbackRule, $sourceSpecificRule]);

        // AssignAgent should be called with the source-specific rule, not the fallback
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($sourceSpecificRule) {
                    return $rule === $sourceSpecificRule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_handles_no_matching_rules(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        // Create rule that doesn't match
        $nonMatchingRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 999, // Different source
            purposeId: 10
        );

        $automationRules = new Collection([$nonMatchingRule]);

        // AssignAgent should NOT be called when no rules match
        $this->assignAgentMock->shouldNotReceive('byRule');

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_handles_enquiry_with_null_purpose(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: null);

        $rule = $this->createRule(trigger: 'new_lead', action: 'assign', sourceId: 5, purposeId: null);

        $automationRules = new Collection([$rule]);

        // AssignAgent should be called since the rule matches
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($r) use ($rule) {
                    return $r === $rule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_combines_source_and_purpose_filtering(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        // Create multiple rules to test the single query logic
        $sourceAndPurposeMatch = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: 10
        );

        $onlySourceMatch = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: 20 // Different purpose
        );

        $generalRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: null,
            purposeId: null
        );

        // The source-specific rule with matching purpose should be selected
        $automationRules = new Collection([$generalRule, $onlySourceMatch, $sourceAndPurposeMatch]);

        // AssignAgent should be called with the most specific matching rule
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($sourceAndPurposeMatch) {
                    return $rule === $sourceAndPurposeMatch;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function new_lead_functions_handles_missing_enquiry(): void
    {
        // AssignAgent should not be called for non-existent enquiry
        $this->assignAgentMock->shouldNotReceive('byRule');
        
        // Test with non-existent lead ID
        $this->service->newLeadFunctions(999999);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function new_lead_functions_handles_empty_automation_rules(): void
    {
        // Create a test enquiry using create method instead of factory
        $enquiry = Enquiry::create([
            'fk_int_user_id' => 9999, // Vendor with no rules
            'created_by' => 1,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '9999999999',
            'fk_int_enquiry_type_id' => 1,
        ]);

        // AssignAgent should not be called when there are no rules
        $this->assignAgentMock->shouldNotReceive('byRule');

        $this->service->newLeadFunctions($enquiry->pk_int_enquiry_id);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_respects_rule_priority(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        // Create rules with different priorities
        $lowPriorityRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: null,
            purposeId: null
        );

        $highPriorityRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 5,
            purposeId: 10
        );

        $automationRules = new Collection([$lowPriorityRule, $highPriorityRule]);

        // AssignAgent should be called with the high priority (source-specific) rule
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($highPriorityRule) {
                    return $rule === $highPriorityRule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    /**
     * @test
     */
    public function handle_new_lead_auto_assign_selects_rule_with_null_source_when_no_source_match(): void
    {
        $enquiry = $this->createMockEnquiry(staffId: null, sourceId: 5, purposeId: 10);

        // Create a rule that matches only by purpose (source is null)
        $purposeMatchRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: null,
            purposeId: 10
        );

        // Create a rule with different source and purpose
        $noMatchRule = $this->createRule(
            trigger: 'new_lead',
            action: 'assign',
            sourceId: 999,
            purposeId: 20
        );

        $automationRules = new Collection([$noMatchRule, $purposeMatchRule]);

        // AssignAgent should be called with the purpose-matching rule
        $this->assignAgentMock->shouldReceive('byRule')
            ->once()
            ->with(
                Mockery::on(function ($rule) use ($purposeMatchRule) {
                    return $rule === $purposeMatchRule;
                }),
                Mockery::on(function ($enq) use ($enquiry) {
                    return $enq === $enquiry;
                })
            );

        $this->invokePrivateMethod('handleNewLeadAutoAssign', [$automationRules, $enquiry]);
        
        // Verify mock expectations were met
        $this->assertTrue(true);
    }

    private function createMockEnquiry(?int $staffId, int $sourceId, ?int $purposeId): Enquiry
    {
        $enquiry = new Enquiry();
        $enquiry->staff_id = $staffId;
        $enquiry->fk_int_enquiry_type_id = $sourceId;
        $enquiry->fk_int_purpose_id = $purposeId;
        $enquiry->fk_int_user_id = 1000;
        $enquiry->pk_int_enquiry_id = rand(1000, 9999);

        return $enquiry;
    }

    private function createRule(string $trigger, string $action, ?int $sourceId, ?int $purposeId): object
    {
        return (object) [
            'trigger' => $trigger,
            'action' => $action,
            'enquiry_source_id' => $sourceId,
            'enquiry_purpose_id' => $purposeId,
            'assign_mode' => 1,
            'assign_id' => 1,
        ];
    }

    private function invokePrivateMethod(string $methodName, array $parameters): void
    {
        $reflection = new ReflectionClass($this->service);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        $method->invokeArgs($this->service, $parameters);
    }
}