<?php

declare(strict_types=1);

namespace Tests\Feature\GlApi;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Event;
use Illuminate\Testing\TestResponse;
use Tests\TestCase;

class CreateNoteControllerTest extends TestCase
{
    use DatabaseTransactions;

    private User $vendor;

    private Enquiry $enquiry;

    protected function setUp(): void
    {
        parent::setUp();

        Event::fake([CreateFollowup::class]);

        // Disable middleware for controller tests
        // We'll manually attach api_user_id to simulate ValidateApiToken middleware behavior
        // This isolates controller testing from middleware concerns
        $this->withoutMiddleware();

        // Create vendor user
        $this->vendor = User::factory()->create([
            'parent_user_id' => 2,
        ]);

        // Create enquiry
        $this->enquiry = Enquiry::create([
            'fk_int_user_id' => $this->vendor->pk_int_user_id,
            'vchr_customer_name' => 'Test Customer',
            'vchr_customer_mobile' => '919876543210',
            'mobile_no' => '9876543210',
            'vchr_customer_email' => '<EMAIL>',
            'fk_int_enquiry_type_id' => 1,
            'feedback_status' => 1,
        ]);
    }

    /**
     * @test
     */
    public function it_creates_note_for_existing_enquiry_using_mobile_number(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'This is a test note',
            'mobileno' => '9876543210',
            'countrycode' => '91',
        ]);

        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);

        Event::assertDispatched(CreateFollowup::class, fn ($event) => $event->note === 'This is a test note'
                && $event->log_type === EnquiryFollowup::TYPE_NOTE
                && $event->enquiry_id === $this->enquiry->pk_int_enquiry_id
                && $event->created_by === $this->vendor->pk_int_user_id);
    }

    /**
     * @test
     */
    public function it_creates_note_for_existing_enquiry_using_email(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'This is a test note via email',
            'email' => '<EMAIL>',
        ]);

        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);

        Event::assertDispatched(CreateFollowup::class, fn ($event) => $event->note === 'This is a test note via email'
            && $event->log_type === EnquiryFollowup::TYPE_NOTE
            && $event->enquiry_id === $this->enquiry->pk_int_enquiry_id
            && $event->created_by === $this->vendor->pk_int_user_id);
    }

    /**
     * @test
     */
    public function it_returns_error_when_enquiry_not_found(): void
    {
        $nonExistentMobile = '9633333583';
        $nonExistentEmail = '<EMAIL>';

        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'This is a test note',
            'mobileno' => $nonExistentMobile,
            'email' => $nonExistentEmail,  // Explicitly provide non-existent email
            'countrycode' => '91',
        ]);

        $response->assertNotFound()
            ->assertJson([
                'status' => 'fail',
                'message' => 'Enquiry not found',
            ]);
    }

    /**
     * @test
     */
    public function it_validates_either_email_or_mobile_is_required(): void
    {
        // Test missing both email and mobile
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note',
            // Missing both email and mobile_no
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mobileno', 'email'])
            ->assertJson([
                'errors' => [
                    'mobileno' => ['Either mobile number or email is required'],
                    'email' => ['Either email or mobile number is required'],
                ],
            ]);
    }

    /**
     * @test
     */
    public function it_accepts_request_with_only_email(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note with email only',
            'email' => '<EMAIL>',
            // No mobile_no or countrycode
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
    }

    /**
     * @test
     */
    public function it_accepts_request_with_only_mobile(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note with mobile only',
            'mobileno' => '9876543210',
            'countrycode' => '91',
            // No email
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);
    }

    /**
     * @test
     */
    public function it_validates_note_max_length(): void
    {
        $longNote = str_repeat('a', 2001); // Exceeds 2000 character limit

        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => $longNote,
            'mobileno' => '9876543210',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['note']);
    }

    /**
     * @test
     */
    public function it_validates_mobile_number_format(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note',
            'mobileno' => 'abc123', // Invalid format
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mobileno']);
    }

    /**
     * @test
     */
    public function it_validates_email_format(): void
    {
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note',
            'email' => 'invalid-email', // Invalid format
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /**
     * @test
     */
    public function it_sets_default_country_code_when_not_provided(): void
    {
        // Use the existing enquiry's mobile number
        $response = $this->postWithApiUser('/api/gl-note-creation-api', [
            'note' => 'Test note with default country code',
            'mobileno' => '9876543210', // Same as the setup enquiry
            // countrycode not provided, should default to 91
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Note added successfully',
            ]);

        Event::assertDispatched(CreateFollowup::class);
    }

    /**
     * Make a request with api_user_id attached to simulate middleware behavior
     */
    private function postWithApiUser(string $uri, array $data = []): TestResponse
    {
        // Add api_user_id to the request data (simulating what ValidateApiToken middleware does)
        $data['api_user_id'] = $this->vendor->pk_int_user_id;

        // Also set it in request attributes to fully simulate middleware behavior
        return $this->postJson($uri, $data);
    }
}
