<?php

declare(strict_types=1);

namespace Tests\Feature\GlApi\Middleware;

use App\BackendModel\GlApiTokens;
use App\GlApi\Http\Middlewares\ValidateApiToken;
use App\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class ValidateApiTokenMiddlewareTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * @test
     */
    public function it_returns_error_when_token_is_missing(): void
    {
        // Create a test route
        Route::post('/test-middleware', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $response = $this->postJson('/test-middleware', [
            'note' => 'Test note',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'status' => 'fail',
                'message' => 'API token is required',
            ]);
    }

    /**
     * @test
     */
    public function it_returns_error_when_token_is_empty(): void
    {
        Route::post('/test-middleware', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $response = $this->postJson('/test-middleware', [
            'token' => '',
            'note' => 'Test note',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'status' => 'fail',
                'message' => 'API token is required',
            ]);
    }

    /**
     * @test
     */
    public function it_returns_error_when_token_is_invalid(): void
    {
        Route::post('/test-middleware', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $response = $this->postJson('/test-middleware', [
            'token' => 'invalid-token-123',
            'note' => 'Test note',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'status' => 'fail',
                'message' => 'Invalid API token',
            ]);
    }

    /**
     * @test
     */
    public function it_passes_through_when_token_is_valid_and_attaches_user_id(): void
    {
        // Create user and token
        $user = User::factory()->create([
            'pk_int_user_id' => 1500,
            'parent_user_id' => 2,
        ]);

        $apiToken = GlApiTokens::create([
            'vchr_token' => 'test-valid-token-123',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        // Create route that accesses the attached user ID
        Route::post('/test-middleware', static fn (Request $request) => response()->json([
            'success' => true,
            'api_user_id' => $request->input('api_user_id'),
            'api_user_id_attribute' => $request->attributes->get('api_user_id'),
        ]))->middleware(ValidateApiToken::class);

        $response = $this->postJson('/test-middleware', [
            'token' => 'test-valid-token-123',
            'note' => 'Test note',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'api_user_id' => 1500,
                'api_user_id_attribute' => 1500,
            ]);
    }

    /**
     * @test
     */
    public function it_preserves_original_request_data(): void
    {
        $user = User::factory()->create([
            'pk_int_user_id' => 1600,
        ]);

        GlApiTokens::create([
            'vchr_token' => 'preserve-test-token',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        Route::post('/test-preserve', static fn (Request $request) => response()->json([
            'all_data' => $request->all(),
        ]))->middleware(ValidateApiToken::class);

        $originalData = [
            'token' => 'preserve-test-token',
            'note' => 'Test note',
            'mobile' => '1234567890',
            'email' => '<EMAIL>',
            'custom_field' => 'custom_value',
        ];

        $response = $this->postJson('/test-preserve', $originalData);

        $response->assertStatus(200);

        $responseData = $response->json('all_data');

        // Check all original data is preserved
        foreach ($originalData as $key => $value) {
            $this->assertEquals($value, $responseData[$key]);
        }

        // Check api_user_id is added
        $this->assertEquals(1600, $responseData['api_user_id']);
    }

    /**
     * @test
     */
    public function it_handles_json_content_type(): void
    {
        $user = User::factory()->create([
            'pk_int_user_id' => 1700,
        ]);
        GlApiTokens::create([
            'vchr_token' => 'json-test-token',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        Route::post('/test-json', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $response = $this->json('POST', '/test-json', [
            'token' => 'json-test-token',
            'data' => 'test',
        ], [
            'Content-Type' => 'application/json',
        ]);

        $response->assertStatus(200);
    }

    /**
     * @test
     */
    public function it_handles_query_string_token(): void
    {
        $user = User::factory()->create([
            'pk_int_user_id' => 1800,
        ]);
        GlApiTokens::create([
            'vchr_token' => 'query-test-token',
            'fk_int_user_id' => $user->pk_int_user_id,
        ]);

        Route::post('/test-query', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $response = $this->post('/test-query?token=query-test-token', [
            'data' => 'test',
        ]);

        $response->assertStatus(200);
    }

    /**
     * @test
     */
    public function it_does_not_expose_full_token_in_logs(): void
    {
        Route::post('/test-log-security', static fn () => response()->json([
            'success' => true,
        ]))->middleware(ValidateApiToken::class);

        $longToken = 'this-is-a-very-long-token-that-should-be-truncated-12345';

        $response = $this->postJson('/test-log-security', [
            'token' => $longToken,
        ]);

        $response->assertStatus(401);

        // In a real test, we would check the logs to ensure only partial token is logged
        // For now, we just verify the response doesn't contain the full token
        $this->assertStringNotContainsString($longToken, $response->getContent());
    }

    /**
     * @test
     */
    public function it_handles_multiple_requests_with_different_tokens(): void
    {
        // Create multiple users and tokens
        $user1 = User::factory()->create([
            'pk_int_user_id' => 1901,
        ]);
        $user2 = User::factory()->create([
            'pk_int_user_id' => 1902,
        ]);

        $token1 = GlApiTokens::create([
            'vchr_token' => 'user1-token',
            'fk_int_user_id' => $user1->pk_int_user_id,
        ]);

        $token2 = GlApiTokens::create([
            'vchr_token' => 'user2-token',
            'fk_int_user_id' => $user2->pk_int_user_id,
        ]);

        Route::post('/test-multi-user', static fn (Request $request) => response()->json([
            'user_id' => $request->attributes->get('api_user_id'),
        ]))->middleware(ValidateApiToken::class);

        // Test with first token
        $response1 = $this->postJson('/test-multi-user', [
            'token' => 'user1-token',
        ]);
        $response1->assertStatus(200)
            ->assertJson([
                'user_id' => 1901,
            ]);

        // Test with second token
        $response2 = $this->postJson('/test-multi-user', [
            'token' => 'user2-token',
        ]);
        $response2->assertStatus(200)
            ->assertJson([
                'user_id' => 1902,
            ]);
    }
}
