@extends('backend.layout.master')
@section('page-header')
@endsection
@push('header.css')
    <link href="https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400&family=Roboto:wght@500&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
@endpush @section('content')
    <style>
        /************************************************ Dashboard ************************************************/

        .big-dot {
            width: 40px;
            height: 40px;
            left: 276px;
            top: 145px;
            background: rgba(255, 35, 61, 0.1);
            border-radius: 50%;
            margin-right: 10px;
        }

        .card-inner.d-flex {
            padding: 10px;
        }

        select.form-select {
            width: 229px;
            height: 40px;
            left: 1188px;
            top: 71px;
            background: #ffffff;
            border-radius: 4px;
        }

        .card-inner h4 {
            font-weight: 600;
            font-size: 18px;
            line-height: 27px;
            color: #121212;
            margin: 0;
        }

        .card-inner span {
            font-style: normal;
            font-weight: normal;
            font-size: 12px;
            line-height: 18px;
            color: #717579;
        }

        .small-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .bg-grn {
            background: #97e0a7;
        }

        .bg-bl {
            background: #95e9e8;
        }

        .bg-vl {
            background: #efb6f9;
        }

        .bg-yl {
            background: #eeefae;
        }

        .bg-dark-gn {
            background: #a9cea8;
        }

        .bg-pnk {
            background: #ffacb8;
        }

        .card-div {
            padding: 0px;
        }

        .dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #000;
            margin: 0 15px 0 25px;
        }

        .border-div {
            border-bottom: 1px solid #f7f9fd;
            padding: 8px 0;
        }

        /* ********************************** */
        /**************************donut and graph *****************************/
        .doughnutChart h6 {
            font-family: Poppins;
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: #171b1e;
            margin-bottom: 20px;
        }

        .doughnutChart {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            margin-top: 0px;
            /* box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px; */
            height: 100%;
        }

        .chart_report {
            padding: 30px;
            height: 100%;
            background: #fff;
        }

        .chart-report-graph {
            display: grid;
            grid-template-columns: 100% auto;
            background: #ffffff;
            border-radius: 12px;
            /* padding: 33px 33px 18px 25px; */
            margin: 30px 0 10px 0;
            /* box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px; */
            margin-top: 0px;
        }

        .deal-dot {
            background: rgb(207 0 57 / 85%) !important;
        }

        .filter-charts {
            width: fit-content;
            margin-left: auto;
        }

        .filter-charts select.form-select {
            background: transparent;
            cursor: pointer;
        }

        .card.leadStage,
        .card.task_status {
            height: 100%;
            margin-bottom: 0;
        }

        /**************************donut and graph ends*****************************/
        .row-v3 {
            margin-top: 30px;
        }

        .row-v3-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            position: sticky;
            top: 0;
            background: #fff;
            /* padding: 20px; */
            z-index: 9;
            padding: 30px;
            width: 100%;
            padding-bottom: 0 !important;
        }

        .lead-box {
            padding: 30px;
            padding-top: 0;
        }

        .row-v3-header h5 {
            margin-bottom: 0px;
        }

        .row-v3-header .filter-charts select.form-select {
            color: #5a5a5a;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        h6 {
            font-weight: 500 !important;
        }

        .countdown-digit {
            font-size: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }
    </style>

    <style>
        .loader,
        .loader:after {
            border-radius: 50%;
            width: 50px;
            height: 50px;
        }
        #loadingDiv img {
            width: 80px !important;
            height: 80px !important;
            object-fit: contain !important;
        }
        #loadingDiv {
            position: fixed;
            top: 0;
            bottom: 0;
            margin: auto;
            right: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgb(246 249 254 / 70%);
            /* background-color: #F6F9FE; */
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
            min-height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: inherit;
        }
        .dashboard-v4 {
            position: relative;
            width: auto;
            height: auto;
        }
        .dashboard-load {
            height: 100%;
            overflow: hidden;
        }
        .select2-container {
            min-width: 12em!important;
        }
    </style>

    <main class="main-wrapper">
        <div class="layout-wrapper">
            <!--   / Side menu included /  -->
            @include ('backend.layout.sidebar-v2.crmsidebar-v2')
            <div class="content-section pt-0 dashboard-v4 dashboard-load">
                <!--  /Your content goes here/ -->
                <!-- Dashboard V3 starts here -->
                <div id="vue-dashboard">
                    <div class="d-flex row-wrap align-items-center">
                        <div class="col-lg-12">
                            <h5 class="head-title pb-1">Dashboard</h5>
                        </div>
                    </div>
                    <div class="content d-flex flex-column flex-column-fluid">
                        <div class="d-flex flex-column-fluid">
                            <!--begin::Container-->
                            <div class="container-fluid">
                                <div class="row dashboard-cols">
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-lg-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>{{ $enquiry_stats?->todayCount ?? 0 }}</h4>
                                                    <span>Today</span>
                                                </div>
                                                <a href="{{ url('user/enquiries') . '?index=today' }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-lg-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>{{ $enquiry_stats?->weekCount ?? 0 }}</h4>
                                                    <span>Week</span>
                                                </div>
                                                <a href="{{ url('user/enquiries') . '?index=week' }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-lg-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>{{ $enquiry_stats?->monthCount ?? 0 }}</h4>
                                                    <span>Month</span>
                                                </div>
                                                <a href="{{ url('user/enquiries') . '?index=month' }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-lg-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>{{ $enquiry_stats?->totalCount ?? 0 }}</h4>
                                                    <span>Total</span>
                                                </div>
                                                <a href="{{ url('user/enquiries') . '?index=total' }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-lg-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>{{ $deal_counts?->totalCount ?? 0 }}</h4>
                                                    <span>Open {{ getDealName() }}</span>
                                                </div>
                                                <a href="{{ url('user/deals?index=inprogress') }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-2 col-md-4">
                                        <div class="card mb-0 dash-box">
                                            <div class="card-inner d-flex">
                                                <div>
                                                    <h4>₹{{ $deal_counts?->totalSum ?? 0 }}</h4>
                                                    <span>Pipeline amount</span>
                                                </div>
                                                <a href="{{ url('user/deals?index=inprogress') }}" class="redir-arrow">
                                                    <svg width="8" height="13" viewBox="0 0 8 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                                d="M0.964013 0.632577C0.799976 0.796663 0.707825 1.01918 0.707825 1.2512C0.707825 1.48322 0.799976 1.70574 0.964013 1.86983L5.29526 6.20108L0.964013 10.5323C0.804625 10.6974 0.716429 10.9184 0.718423 11.1478C0.720416 11.3772 0.81244 11.5967 0.974672 11.7589C1.1369 11.9212 1.35636 12.0132 1.58579 12.0152C1.81521 12.0172 2.03624 11.929 2.20126 11.7696L7.15114 6.8197C7.31518 6.65562 7.40733 6.4331 7.40733 6.20108C7.40733 5.96906 7.31518 5.74654 7.15114 5.58245L2.20126 0.632577C2.03718 0.46854 1.81466 0.376389 1.58264 0.376389C1.35062 0.376389 1.1281 0.46854 0.964013 0.632577Z"
                                                                fill="white"
                                                        />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4 p-3"></div>
                                    <div class="col-lg-8">
                                        <form id="filter_dashboard" class="filter-charts d-flex">
                                            @if(App\Common\Variables::checkEnableSettings('branch-filter'))
                                                <select name="branch_id" id="branch_id" class="form-control">
                                                    <option value="">Select Branch</option>
                                                    @foreach($branches as $branch)
                                                        <option value="{{ $branch['id'] }}">{{ $branch['branch'] }}</option>
                                                    @endforeach
                                                </select>
                                            @else
                                                <input type="hidden" name="branch_id" id="branch_id" value="" />
                                            @endif
                                            <input type="hidden" name="vendor_id" id="vendor_id" value="{{ $vendor_id }}" />
                                            <select name="date_filter" id="date_filter" class="form-select w-auto" aria-label="Default select example" style="border: none; padding-left: 10px;">
                                                <option value="today">Today</option>
                                                <option value="yesterday">Yesterday</option>
                                                <option value="last_7_days">Last 7 days</option>
                                                <option value="this_week">This week</option>
                                                <option value="last_week">Last week</option>
                                                <option value="last_30_days" selected>Last 30 days</option>
                                                <option value="this_month">This month</option>
                                                <option value="last_month">Last month</option>
                                                <option value="this_year">This year</option>
                                                <option value="">Lifetime</option>
                                                <option value="custom">Custom date</option>
                                            </select>

                                            <div id="reportrange" class="pull-left w-100 ml-2" style="background: #fff; cursor: pointer; padding: 10px; border: 1px solid #ccc;height:41.6px;border-radius:4px">
                                                <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp; <span class="dateRange"></span> <b class="caret"></b>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6 col-md-12 col-sm-12">
                                        <div class="chart_report mb-0">
                                            <h6 class="mt-0"><span class="duration">Last 30 days</span> added Leads</h6>
                                            <div class="chart-report-graph">
                                                <div id="chart"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-12 col-md-6">
                                        <div class="card leadStage">
                                            <div class="card-inner">
                                                <div class="card-div">
                                                    <div class="row-v3-header">
                                                        <h5>Lead status</h5>
                                                    </div>
                                                    <div id="lead_status" class="lead-box"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-12 col-md-6">
                                        <div class="card task_status">
                                            <div class="card-inner">
                                                <div class="card-div">
                                                    <div class="row-v3-header">
                                                        <h5>Task status</h5>
                                                    </div>
                                                    <div class="lead-box">
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-grn"></div>
                                                                <h6 class="m-0">Total task</h6>
                                                            </div>
                                                            <h4 id="task_total"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-bl"></div>
                                                                <h6 class="m-0">Pending</h6>
                                                            </div>
                                                            <h4 id="task_pending"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-vl"></div>
                                                                <h6 class="m-0">Overdue</h6>
                                                            </div>
                                                            <h4 id="task_overdue"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-yl"></div>
                                                                <h6 class="m-0">Completed</h6>
                                                            </div>
                                                            <h4 id="task_completed"></h4>
                                                        </div>
                                                    </div>
                                                    <div class="row-v3-header" style="padding-top:0 !important;">
                                                        <h5>Call status</h5>
                                                    </div>
                                                    <div class="lead-box">
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-grn"></div>
                                                                <h6 class="m-0">Total task</h6>
                                                            </div>
                                                            <h4 id="call_task_total"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-bl"></div>
                                                                <h6 class="m-0">Pending</h6>
                                                            </div>
                                                            <h4 id="call_task_pending"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-vl"></div>
                                                                <h6 class="m-0">Overdue</h6>
                                                            </div>
                                                            <h4 id="call_task_overdue"></h4>
                                                        </div>
                                                        <div class="d-flex justify-content-between border-div">
                                                            <div class="d-flex align-items-center">
                                                                <div class="small-dot bg-yl"></div>
                                                                <h6 class="m-0">Completed</h6>
                                                            </div>
                                                            <h4 id="call_task_completed"></h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row row-v3">
                                    <div class="col-lg-3 col-md-6 col-sm-12">
                                        <div class="card leadStage">
                                            <div class="card-inner">
                                                <div class="card-div">
                                                    <div class="row-v3-header">
                                                        <h5>{{ getDealName(true) }} pipeline</h5>
                                                    </div>
                                                    <div id="deal-pipeline" class="lead-box"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12">
                                        <div class="card leadStage">
                                            <div class="card-inner">
                                                <div class="card-div">
                                                    <div class="row-v3-header">
                                                        <h5>{{ getDealName() }} in progress</h5>
                                                    </div>
                                                    <div id="agent-wise-deals" class="lead-box"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12">
                                        <div class="doughnutChart">
                                            <h6 class="mt-0">Lead source</h6>
                                            <div class="pie-outer-div" style="height: 100%; margin: auto;">
                                                <div id="doughnut-lead-source"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12">
                                        <div class="doughnutChart">
                                            <h6 class="mt-0">Lead purpose</h6>
                                            <div class="pie-outer-div" style="height: 100%; margin: auto;">
                                                <div id="doughnut-lead-purpose"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Row-->
                                <!--end::Dashboard-->
                            </div>
                            <!--end::Container-->
                        </div>
                    </div>
                </div>
                <!-- Dashboard V3 ends here -->
            </div>
        </div>
    </main>
    <!-- Plus Button -->
@endsection @push('footer.script')
    <!--end::Demo Panel-->
    <script>
        var HOST_URL = "https://preview.keenthemes.com/metronic/theme/html/tools/preview";
    </script>
    <script type="text/javascript" src="{{ url('backend/js/daterangepicker.js')}}"></script>
    <!--begin::Global Config(global config for global JS scripts)-->
    <script>
        var KTAppSettings = {
            breakpoints: {
                sm: 576,
                md: 768,
                lg: 992,
                xl: 1200,
                xxl: 1200,
            },
            colors: {
                theme: {
                    base: {
                        white: "#ffffff",
                        primary: "#1BC5BD",
                        secondary: "#E5EAEE",
                        success: "#1BC5BD",
                        info: "#6993FF",
                        warning: "#FFA800",
                        danger: "#F64E60",
                        light: "#F3F6F9",
                        dark: "#212121",
                    },
                    light: {
                        white: "#ffffff",
                        primary: "#1BC5BD",
                        secondary: "#ECF0F3",
                        success: "#C9F7F5",
                        info: "#E1E9FF",
                        warning: "#FFF4DE",
                        danger: "#FFE2E5",
                        light: "#F3F6F9",
                        dark: "#D6D6E0",
                    },
                    inverse: {
                        white: "#ffffff",
                        primary: "#ffffff",
                        secondary: "#212121",
                        success: "#ffffff",
                        info: "#ffffff",
                        warning: "#ffffff",
                        danger: "#ffffff",
                        light: "#464E5F",
                        dark: "#ffffff",
                    },
                },
                gray: {
                    "gray-100": "#F3F6F9",
                    "gray-200": "#ECF0F3",
                    "gray-300": "#E5EAEE",
                    "gray-400": "#D6D6E0",
                    "gray-500": "#B5B5C3",
                    "gray-600": "#80808F",
                    "gray-700": "#464E5F",
                    "gray-800": "#1B283F",
                    "gray-900": "#212121",
                },
            },
            "font-family": "Poppins",
        };
    </script>
    <!--end::Global Config-->
    <!--begin::Global Theme Bundle(used by all pages)-->
    <script src="{{ asset('backend/js/plugins.bundle.js') }}"></script>
    <script src="{{ asset('backend/js/prismjs.bundle.js') }}"></script>
    <script src="{{ asset('backend/js/scripts.bundle.js') }}"></script>
    <!--end::Global Theme Bundle-->
    <!--begin::Page Vendors(used by this page)-->
    <!--end::Page Vendors-->
    <!--begin::Page Scripts(used by this page)-->
    <script src="{{ asset('backend/js/widgets.js') }}"></script>
    <!--end::Page Scripts-->
    <script src="{{ asset('backend/js/draggable.bundle.js') }}"></script>
    <script src="{{ asset('backend/js/draggable.js') }}"></script>
    <script src="{{ asset('apexcharts-bundle/js/apexcharts.js') }}"></script>

    @if ('local' === config('app.env'))
        <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
    @else
        <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    @endif {{------------------------------ Loader ------------------------------}} {{--
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
--}}

    <script>
        function loadLoader() {
            $("body .dashboard-v4").append(
                '<div style="" id="loadingDiv">\
                    <img src="/backend/images/preloader.gif" alt="Getlead" style="width:48px;height:48px;">\
                </div>'
            );
            setTimeout(removeLoader, 2000);
        }
        window.onload = loadLoader();

        function removeLoader() {
            $("#loadingDiv").fadeOut(1000, function () {
                // fadeOut complete. Remove the loading div
                $("#loadingDiv").remove(); //makes page more lightweight
            });
        }
    </script>

    {{-- ----------------- Bar graph ---------------------------- --}}
    <script type="text/javascript" src="//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.css" />
    <script>
        (function(d,t) {
          var BASE_URL="https://app.chatwoot.com";
          var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
          g.src=BASE_URL+"/packs/js/sdk.js";
          g.defer = true;
          g.async = true;
          s.parentNode.insertBefore(g,s);
          g.onload=function(){
            window.chatwootSDK.run({
              websiteToken: 'Rb76G98NV2P1TNH3cUk7mDdH',
              baseUrl: BASE_URL
            })
          }
        })(document,"script");
    </script>
    <script>
        var apxchart = null;
        var pie_charts = [];
        var pie_source = {};
        var pie_purpose = {};
        $(document).ready(function () {
            $("#reportrange").hide();
            removeLoader();
            taskStatus();
            CallStatus();
            leadStatusBarDiagram(0);
            chartData(0);
            @if(App\Common\Variables::checkEnableSettings('branch-filter'))
            $('#branch_id').select2();
            @endif
            $("#date_filter").on("change", function () {
                filterDashboard()
            });

            $("#branch_id").on("change", function () {
                filterDashboard()
            });


            function filterDashboard(){
                var text = $("#date_filter").find(":selected").text();
                $(".duration").html(text);
                if ($("#date_filter").val() == "custom") {
                    $("#reportrange").show();
                    taskStatus();
                    CallStatus();
                    leadStatusBarDiagram(1);
                    chartData(1);
                } else {
                    $("#reportrange").hide();
                    taskStatus();
                    CallStatus();
                    leadStatusBarDiagram(1);
                    chartData(1);
                }
            }

            $(document).on("click", ".applyBtn", function () {
                loadLoader();
                taskStatus();
                CallStatus();
                leadStatusBarDiagram(1);
                chartData(1);
            });

            var start = moment().subtract(29, "days");
            var end = moment();

            function cb(start, end) {
                $("#reportrange span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"));
            }

            $("#reportrange").daterangepicker(
                {
                    startDate: start,
                    endDate: end,
                },
                cb
            );

            cb(start, end);
        });

        function taskStatus() {
            if ($("#date_filter").val() == "custom") {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    filter_by: $("#date_filter").val(),
                    branch_id: $("#branch_id").val(),
                    date: $("#reportrange span").html(),
                };
            } else {
                loadLoader();
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                };
            }
            $.ajax({
                type: "POST",
                url: "{{ route('task-status') }}",
                data: JSON.stringify(data),
                contentType: "application/json",
                headers: {
                    "Accept": "application/json"
                },
                success: function (res) {
                    removeLoader();
                    $("#task_total").html(res.data.data.total);
                    $("#task_completed").html(res.data.data.completed);
                    $("#task_overdue").html(res.data.data.overdue);
                    $("#task_pending").html(res.data.data.pending);
                },
            });
        }
        function CallStatus()
        {
            if ($("#date_filter").val() == "custom") {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    filter_by: $("#date_filter").val(),
                    branch_id: $("#branch_id").val(),
                    date: $("#reportrange span").html(),
                };
            } else {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                };
            }
            $.ajax({
                type: "POST",
                url: "{{ route('call-status') }}",
                data: JSON.stringify(data),
                contentType: "application/json",
                headers: {
                    "Accept": "application/json"
                },
                success: function (res) {

                    $("#call_task_total").html(res.data.data.total);
                    $("#call_task_completed").html(res.data.data.completed);
                    $("#call_task_overdue").html(res.data.data.overdue);
                    $("#call_task_pending").html(res.data.data.pending);

                },
            });
        }

        function leadStatusBarDiagram(flag) {
            if ($("#date_filter").val() == "custom") {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                    date: $("#reportrange span").html(),
                };
            } else {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                };
            }
            $.ajax({
                type: "POST",
                url: "{{ route('lead-status') }}",
                data: JSON.stringify(data),
                contentType: "application/json",
                headers: {
                    "Accept": "application/json"
                },
                success: function (res) {
                    var html = "";
                    $.each(res.data.data.feedback_status, function (arrayIndex, elementValue) {
                        html =
                            html +
                            `<div  class="d-flex justify-content-between border-div" id="lead_status">
                              <div class="d-flex align-items-center">
                                 <div class="small-dot bg-grn" style="background:` +
                            elementValue.vchr_color +
                            `"></div>
                                 <h6 class="m-0">` +
                            elementValue.vchr_status +
                            `</h6>
                              </div>
                              <h4>` +
                            elementValue.total +
                            `</h4>
                           </div>`;
                    });
                    $("#lead_status").html(html);

                    //bar chart
                    if (flag == 0) {
                        apxchart = new ApexCharts(
                            document.querySelector("#chart"),
                            (line_chart_options = {
                                chart: {
                                    type: "bar",
                                    width: "100%",
                                    height: 300,
                                    zoom: {
                                        enabled: false,
                                    },
                                },
                                stroke: {
                                    //  curve: "smooth",
                                    //  colors: ["#216FED"],
                                    width: 1,
                                    colors: ["#fff"],
                                },
                                plotOptions: {
                                    bar: {
                                        columnWidth: "100%",
                                    },
                                },
                                dataLabels: {
                                    enabled: false,
                                },
                                colors: "#FF5367",
                                series: [
                                    {
                                        name: "Leads",
                                        data: res.data.data.line_graph,
                                        color:"#FF5367",
                                    },
                                ],
                                xaxis: {
                                    categories: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
                                },
                            })
                        );
                        apxchart.render();
                    } else {
                        apxchart.updateSeries([
                            {
                                name: "Leads",
                                data: res.data.data.line_graph,
                            },
                        ]);
                    }
                },
            });
        }

        function chartData(flag) {
            if ($("#date_filter").val() == "custom") {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                    date: $("#reportrange span").html(),
                };
            } else {
                var data = {
                    vendor_id: $("#vendor_id").val(),
                    branch_id: $("#branch_id").val(),
                    filter_by: $("#date_filter").val(),
                };
            }
            $.ajax({
                type: "POST",
                url: "{{ route('chart-status') }}",
                contentType: "application/json",
                headers: {
                    "Accept": "application/json"
                },
                data: JSON.stringify(data),
                success: function (res) {
                    var html = "";
                    $.each(res.data.data.deal_pipeline, function (arrayIndex, elementValue) {
                        const randomColor = "#" + Math.floor(Math.random()*16777215).toString(16);
                        html =
                            html +
                            '<div class="border-div d-flex align-items-center justify-content-between">\
                                               <div class="d-flex align-items-center">\
                                                  <div class="small-dot bg-grn" style="background:'+randomColor+';">\
                                              </div>\
                                              <h6 class="m-0">' +
                            elementValue.deal_stage_name +
                            "</h6>\
                                               </div>\
                                         <h4>" +
                            (elementValue.count == null ? 0 : elementValue.count) +
                            "</h4>\
                                         </div>";
                    });

                    $("#deal-pipeline").html(html);

                    var agentData = "";
                    $.each(res.data.data.agent_wise_deals, function (key, value) {
                        agentData =
                            agentData +
                            '<div class="d-flex border-div align-items-center justify-content-between">\
                                                           <h6 class="m-0">' +
                            value.vchr_user_name +
                            "</h6>\
                                                           <h4>₹" +
                            (value.total_sum != null ?  value.total_sum : 0)+
                            "</h4>\
                                                     </div>";
                    });
                    $("#agent-wise-deals").html(agentData);
                    if (flag == 0) {
                        pie_sources = initializePieChart("doughnut-lead-source", res.data.data.lead_sorce);
                        pie_purposes = initializePieChart("doughnut-lead-purpose", res.data.data.lead_purpose);

                        pie_sources.render();
                        pie_purposes.render();
                        flag = 1;
                    }
                    else {
                        pie_sources.destroy();
                        pie_purposes.destroy();
                        pie_sources = initializePieChart("doughnut-lead-source", res.data.data.lead_sorce);
                        pie_purposes = initializePieChart("doughnut-lead-purpose", res.data.data.lead_purpose);
                        pie_sources.render();
                        pie_purposes.render();
                    }
                },
            });
        }
    </script>
    {{-- ---------------------- Pie chart --------------------------- --}}
    <script>
        var pieOptions = {
            chart: {
                type: "pie",
            },
            dataLabels: {
                enabled: false,
            },
            legend: {
                show: false,
            },
            series: [50, 10, 30, 10, 10, 30],
            colors: ["#31B6B5", "#564DBA", "#F45F82", "#867AFC", "#FDCD49", "#FF4499"],
        };
    </script>
    <script src="{{ asset('backend/js/chart.js') }}" type="text/javascript"></script>
    <script>
        var all_date = "";
        function initializePieChart(dom_id, data) {
            console.log('chart data-');
            console.log(data.series);
            console.log(data.labels);
            var pieOptions = {
                chart: {
                    type: "pie",
                },
                dataLabels: {
                    enabled: true,
                },
                dataLabels: {
                    formatter: function (val, opts) {
                        return opts.w.config.series[opts.seriesIndex];
                    },
                },
                legend: {
                    show: false,
                },
                series: Array.isArray(data.series) ? data.series : [],
                labels: Array.isArray(data.labels) ? data.labels : [],
            };
            var chartElement = document.querySelector("#" + dom_id);
            if (!chartElement) {
                console.error("DOM element with ID " + dom_id + " not found.");
                return;
            }

            try {
                var obj = new ApexCharts(chartElement, pieOptions);
                return obj;
            } catch (error) {
                console.log("Failed to initialize ApexCharts pie chart:");
                console.log(error);
            }
        }
    </script>
@endpush
