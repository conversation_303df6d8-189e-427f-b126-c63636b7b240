<?php

declare(strict_types=1);

namespace App\Enquiry\LeadAutomation\Actions;

use App\AgentDepartment;
use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\Events\LeadAssigned;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class AutoAssign
{
    public function with(AutomationRule $rule, Enquiry $enquiry): void
    {
        Log::withContext([
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'vendor_id' => $enquiry->fk_int_user_id,
            'rule_id' => $rule->id,
        ])->info('Preparing to auto assign enquiry to staff by automation rule');

        //assign mode 1 means assigning to staff
        if ($rule->assign_mode == 1 && $rule->assign_id != null) {
            Log::info('Auto assigning enquiry to staff', [
                'staff_id' => $rule->assign_id,
            ]);
            AgentDepartment::query()
                ->where('vendor_id', $enquiry->fk_int_user_id)
                ->where('agent_id', $rule->assign_id)
                ->increment('count');

            Enquiry::query()
                ->whereKey($enquiry->pk_int_enquiry_id)
                ->update([
                    'staff_id' => $rule->assign_id,
                    'assigned_date' => Carbon::now(),
                ]);

            Event::dispatch(new LeadAssigned(enquiry_id: $enquiry->pk_int_enquiry_id, staff_id: $enquiry->created_by));
            return;
        }

        Log::info('Auto assigning enquiry to department', [
            'department_id' => $rule->assign_id,
        ]);

        $staff = AgentDepartment::query()
            ->where('vendor_id', $enquiry->fk_int_user_id)
            ->where('department_id', $rule->assign_id)
            ->orderBy('count')
            ->first();

        if (!($staff instanceof AgentDepartment)) {
            return;
        }

        $staff->increment('count');

        Enquiry::query()
            ->whereKey($enquiry->pk_int_enquiry_id)
            ->update([
                'staff_id' => $staff->agent_id,
                'assigned_date' => Carbon::now(),
            ]);

        Event::dispatch(new LeadAssigned(enquiry_id: $enquiry->pk_int_enquiry_id, staff_id: $enquiry->created_by));
    }
}
