<?php

declare(strict_types=1);

namespace App\Enquiry\LeadAutomation;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\Enquiry\LeadAutomation\Actions\AutoAssign;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class NewLeadAutomationExecutor
{
    public function __construct(
        public readonly AutoAssign $autoAssign
    ) {
    }

    public function for(int $enquiryId, int $vendorId): void
    {
        Log::withContext([
            'enquiry_id' => $enquiryId,
            'vendor_id' => $vendorId,
        ])->info('Executing lead automation');

        $enquiry = Enquiry::query()
            ->select(
                ['pk_int_enquiry_id',
                'fk_int_enquiry_type_id',
                'fk_int_purpose_id',
                'staff_id',
                'fk_int_user_id',
                'created_by'
            ])
            ->whereKey($enquiryId)
            ->where('fk_int_user_id', $vendorId)
            ->first();

        if (! $enquiry instanceof Enquiry) {
            Log::info('Unable to find enquiry for lead automation');
            return;
        }

        $purposeId = $enquiry->fk_int_purpose_id;

        Log::withContext([
            'enquiry_id' => $enquiryId,
            'vendor_id' => $vendorId,
            'source_id' => $enquiry->fk_int_enquiry_type_id,
            'purpose_id' => $purposeId,
        ]);

        $rules = AutomationRule::query()
            ->whereTrigger('new_lead')
            ->whereVendorId($vendorId)
            ->where(static function (Builder $query) use ($purposeId): void {
                $query->where('enquiry_purpose_id', '=', $purposeId)
                    ->orWhereNull('enquiry_purpose_id');
            })
            ->orderByDesc('id')
            ->get();

        if ($rules->isEmpty()) {
            Log::info('No rules found for lead automation');
            return;
        }

        if (! $enquiry->staff_id) {
            Log::info('Lead has no agent assigned, preparing to auto assign agent');

            $rule = $rules->where('action', 'assign')
                ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                ->filter(
                    static fn ($rule) => $rule->enquiry_purpose_id === null || $rule->enquiry_purpose_id === $purposeId
                )
                ->first()
                ?? $rules->where('action', 'assign')
                    ->filter(
                        static fn ($rule) => $rule->enquiry_purpose_id === null || $rule->enquiry_purpose_id === $purposeId
                    )
                    ->first();

            if ($rule instanceof AutomationRule) {
                $this->autoAssign->with(rule: $rule, enquiry: $enquiry);
            }
        }

        Log::info('Lead automation executed');
    }
}
