<?php

namespace App\Http\Controllers\BackendController;

use App\Agency;
use App\AgentStaff;
use App\AutomationRule;
use App\BackendModel\Country;
use App\BackendModel\District;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryStatus;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\Privilege;
use App\BackendModel\Proposal;
use App\BackendModel\SmsApiCredentials;
use App\BackendModel\SmsDomain;
use App\BackendModel\SmsTemplateCode;
use App\BackendModel\Taluk;
use App\BackendModel\WhatsappTemplate;
use App\Branch;
use App\CallMaster;
use App\CloudTelephonySetting;
use App\Common\Common;
use App\Common\SingleSMS;
use App\Common\Variables;
use App\Constants\MarbleGallery;
use App\Core\CustomClass;
use App\Currency;
use App\EnquiryFieldCustomise;
use App\Events\CreateFollowup;
use App\Events\LeadAssigned;
use App\ExportHistory;
use App\Exports\ImportExport;
use App\FeatureRelease\Facade\FeatureReleaseChecker;
use App\FeatureRelease\TargetContext;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Http\Controllers\Controller;
use App\Imports\ExcelImport;
use App\Ivr\Models\Ivr;
use App\Jobs\LeadImportJob;
use App\LeadImport;
use App\Notifications\AssignLeadToStaff;
use App\SmsPanel;
use App\Subscription\UserServiceSubscription;
use App\Task;
use App\Traits\PosTrait;
use App\User;
use App\UserWhatsappSession;
use Carbon\Carbon;
use Getlead\Campaign\Events\LeadRemovedFromCampaign;
use Getlead\Campaign\Jobs\AttachLeadsToCampaign;
use Getlead\Campaign\Models\CampaignLead;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Sales\Models\Order;
use Getlead\Sales\Models\OrderItem;
use Getlead\Sales\Models\Product;
use GuzzleHttp;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Laracasts\Flash\Flash;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class EnquiryController extends Controller
{
    use PosTrait;

    //web
    public function index(Request $request)
    {
        /**
         * @var User $user
         */
        $user = $request->user();

        $vendorId = $user->getBusinessId();

        $whatsapp_session = UserWhatsappSession::query()
            ->where([
                'fk_int_user_id' => $vendorId,
                'status' => 1,
            ])->first();

        $district = District::query()
            ->where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();

        $taluk = Taluk::query()
            ->where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();

        $currencies = Currency::query()
            ->where('status', 1)
            ->get();

        $userId = $vendorId;

        $types = EnquiryType::query()
            ->where('int_status', '1')
            ->UsedOnly($vendorId)
            ->orWhere('vendor_id', $vendorId)
            ->get();

        $leadTypes = LeadType::getLeadTypes();

        $agents = User::active()
            ->where(function ($q) use ($vendorId) {
                $q->where('pk_int_user_id', $vendorId)
                    ->orWhere('parent_user_id', $vendorId);
            })->get();

        if ($vendorId == Variables::EZZAT_USER_ID && $user->isOpsStaff()) {
            $staffId = User::query()
                ->select(['pk_int_user_id', 'vchr_user_name'])
                ->whereIn('pk_int_user_id', static function ($subQuery) use (
                    $user
                ): void {
                    $subQuery->select('staff_id')
                        ->from('agent_staffs')
                        ->where('agent_id', $user->pk_int_user_id);
                })->orWhere('pk_int_user_id', $userId)
                ->get();
        } else {
            $staffId = User::query()
                ->where(function ($q) use ($vendorId) {
                    $q->where('pk_int_user_id', $vendorId)
                        ->orWhere('parent_user_id', $vendorId);
                });

            if ($user->isOpsStaff()) {
                $assignedStafId = AgentStaff::query()
                    ->where('agent_id', $user->pk_int_user_id)
                    ->pluck('staff_id')
                    ->toArray();
                $staffId = $staffId->where(function ($where) use ($user, $assignedStafId) {
                    $where->where('pk_int_user_id', $user->pk_int_user_id)
                        ->orWhereIn('pk_int_user_id', $assignedStafId);
                });
            }

            $staffId = $staffId->StaffCheck()
                ->whereNotIn('pk_int_user_id', ['3642', '4015'])
                ->get();
        }

        $countrys = Country::All();
        $countryCodeIp = "IN";

        $countryCode = Country::query()
            ->where('country_code', $countryCodeIp)
            ->value('code') ?? '';

        $date_by = collect([
            ['id' => 1, 'name' => 'Created'],
            ['id' => 2, 'name' => 'Updated'],
            ['id' => 3, 'name' => 'Assigned']
        ]);

        $additional_fields = LeadAdditionalField::query()
            ->where('vendor_id', $vendorId)
            ->get();

        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }

        $additionalRequiredDropdown = (clone $additional_fields)
            ->where('input_type', 2)
            ->where('is_required', 1)
            ->values();

        $fields = EnquiryFieldCustomise::query()
            ->where('vendor_id', $vendorId)
            ->first();

        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $purposes = EnquiryPurpose::query()->where('fk_int_user_id', $userId)->get();
        $enquiry_status = FeedbackStatus::query()->UsedStatuses($vendorId)->get();
        $agencies = Agency::query()->where('vendor_id', $userId)->select('id', 'name')->get();

        $lead_campaigns = LeadCampaign::query()
            ->select('id', 'name')
            ->where('vendor_id', $vendorId)
            ->when(request('unassigned'), function ($q) {
                return $q->where('type', LeadCampaign::POOL);
            }, function ($q) {
                return $q->whereNotIn('type', [LeadCampaign::POOL]);
            })
            ->get();

        $campaign_id = $request->has('campaign_id') ?? 0;

        $campaign_type = 0;
        $cmp = LeadCampaign::query()->whereKey($request->get('campaign_id'))->first();

        if ($cmp) {
            $campaign_type = $cmp->type == LeadCampaign::POOL ? 1 : 0;
        }

        $branches = Branch::query()
            ->where('vendor_id', $vendorId)
            ->where('status', 1)
            ->get();
        // for ivr lead report
        $telephony = CloudTelephonySetting::where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();

        if ($telephony) {
            if ($telephony->operator == 2) {
                $dids = CallMaster::where('vendor_id', $vendorId)
                    ->distinct()
                    ->where(function ($q) {
                        $q->whereNotNull('did')
                            ->whereNotIn('did', [2147483647]);
                    })
                    ->get(['did as did']);
            } else {
                $dids = Ivr::where('vendor_id', $vendorId)
                    ->distinct()
                    ->get(['called_number as did']);
            }
        } else {
            $dids = Ivr::where('vendor_id', $vendorId)
                ->distinct()
                ->get(['called_number as did']);
        }

        $showImportContact = $user->hasAccessToFeature('import-v2');

        $canEditSource = Variables::checkEnableSettings('staff-can-update-enquiry-source'); //only user + co-admin can edit any leads
        if ($user->isOpsUser()) {
            $canEditSource = true;
            $created = User::withTrashed()
                ->where('pk_int_user_id', Auth::user()->pk_int_user_id)
                ->first();

            $created_bys = User::StaffCheck()
                ->whereNotIn('pk_int_user_id', ['3642', '4015'])
                ->where('parent_user_id', $vendorId)
                ->orWhere('pk_int_user_id', $vendorId)
                ->get();

            return view(
                'backend.pages.enquiries.index' . (request('fc') ? '-fc' : ''),
                compact(
                    'campaign_id',
                    'date_by',
                    'active_fields',
                    'required_fields',
                    'types',
                    'whatsapp_session',
                    'purposes',
                    'enquiry_status',
                    'created',
                    'created_bys',
                    'staffId',
                    'countrys',
                    'countryCode',
                    'additional_fields',
                    'district',
                    'taluk',
                    'lead_campaigns',
                    'agents',
                    'additionalRequiredDropdown',
                    'currencies',
                    'branches',
                    'leadTypes',
                    'dids',
                    'campaign_type',
                    'agencies',
                    'showImportContact',
                    'canEditSource'
                )
            );
        }

        $created = User::withTrashed()
            ->where('pk_int_user_id', Auth::user()->pk_int_user_id)
            ->first();
        $created_bys = User::StaffCheck()
            ->where('parent_user_id', Auth::user()->parent_user_id)
            ->get();

        return View(
            'backend.pages.enquiries.index' . (request('fc') ? '-fc' : ''),
            compact(
                'campaign_id',
                'date_by',
                'active_fields',
                'required_fields',
                'types',
                'whatsapp_session',
                'purposes',
                'enquiry_status',
                'created',
                'created_bys',
                'staffId',
                'countrys',
                'countryCode',
                'additional_fields',
                'district',
                'taluk',
                'lead_campaigns',
                'agents',
                'additionalRequiredDropdown',
                'currencies',
                'branches',
                'leadTypes',
                'dids',
                'campaign_type',
                'agencies',
                'showImportContact',
                'canEditSource'
            )
        );
    }

    public function displayContent()
    {
        $date_by = collect();
        $date_by->push(['id' => 1, 'name' => 'Created']);
        $date_by->push(['id' => 2, 'name' => 'Updated']);

        $enquiry_display_fields = Auth::user()->enquiry_display_fields;
        if (!$enquiry_display_fields) {
            Auth::user()->update(["enquiry_display_fields" => '["1","2","3","4","5","6","7","8","9","10","11","12","13","14","15"]']);
        }
        $enquiry_display_fields = Auth::user()->enquiry_display_fields;
        $display_fields = Enquiry::DISPLAY_FIELDS;
        $additional_fields = LeadAdditionalField::where('vendor_id', User::getVendorId())->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }

        $data = ['enquiry_display_fields' => $enquiry_display_fields,
            'display_fields' => $display_fields,
            'additional_fields' => $additional_fields
        ];

        return $data;

    }

    public function getFollowUpReqPage()
    {
        $userId = User::getVendorId();
        $district = District::where('vendor_id', $userId)
            ->where('status', 1)
            ->get();

        $types = EnquiryType::where('int_status', '1')
            ->UsedOnly($userId)
            //   ->where('fk_int_user_id',$userId)
            ->get();
        $staffId = User::where('int_role_id', User::STAFF)
            ->where('parent_user_id', $userId)
            ->get();
        $countrys = Country::All();
        $ip = Common::getIp();
        $data = \Location::get($ip);
        if (!$data) {
            $countryCodeIp = "IN";
        } else {
            $countryCodeIp = $data->countryCode;
        }


        $countryNewCheck = Country::where('country_code', $countryCodeIp)->first();
        if ($countryNewCheck) {
            $countryCode = $countryNewCheck->code;
        } else {
            $countryCode = '';
        }
        $additional_fields = LeadAdditionalField::where('vendor_id', $userId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }

        $purposes = EnquiryPurpose::where('fk_int_user_id', $userId)->get();
        $enquiry_status = FeedbackStatus::where('fk_int_user_id', $userId)->get();
        if (Auth::user()->int_role_id == User::USERS) {
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::where('parent_user_id', Auth::user()->pk_int_user_id)->get();
            return View('backend.pages.enquiries.feedback-required-list', compact('types', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'additional_fields', 'district'));
        } elseif (Auth::user()->int_role_id == User::STAFF) {
            //echo Auth::user()->parent_user_id; die();
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::where('parent_user_id', Auth::user()->parent_user_id)->get();
            return View('backend.pages.enquiries.feedback-required-list', compact('types', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'additional_fields', 'district'));
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function adminindex()
    {
        $userId = User::getVendorId();
        $types = EnquiryType::where('int_status', '1')->get();
        $purposes = EnquiryPurpose::all();
        return View('backend.pages.enquiries.adminindex', compact('types', 'purposes'));
    }

    public function getFollowUpReqData(Request $request)
    {
        $vendorId = User::getVendorId();
        $userData = Auth::user();
        //get day,month,year from date
        if ($request->created_at != null) {
            $created_at = $request->created_at;
            $explodeCreatedAt = explode(' ', $created_at);
            $date = $explodeCreatedAt[0];
            $explodeDate = explode('-', $date);
            $year = $explodeDate[0];
            $month = $explodeDate[1];
            $day = $explodeDate[2];
        }
        $assignedStafId = AgentStaff::where('agent_id', $userData->pk_int_user_id)
            ->pluck('staff_id')
            ->toArray();

        $serviceSubscription = UserServiceSubscription::where('vendor_id', $vendorId)
            ->where('service', Variables::SERVICE_CRM)
            ->first();
        if ($serviceSubscription) {
            $userCount = $serviceSubscription->user_count;
        } else {
            $userCount = null;
        }
        if ($userData->int_role_id == Variables::USER || $userData->int_role_id == Variables::STAFF) {
            $enquiries = Enquiry::with(['last_followup'])
                ->leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
                ->leftJoin('tbl_enquiry_purpose as pp', 'tbl_enquiries.fk_int_purpose_id', '=', 'pp.pk_int_purpose_id')
                ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
                ->leftJoin('lead_types', function ($join) use ($vendorId) {
                    $join->on('tbl_enquiries.lead_type_id', '=', 'lead_types.id')->on('lead_types.vendor_id', '=', DB::raw($vendorId));
                })
                ->select(
                    'vchr_customer_name',
                    DB::raw('IFNULL(lead_types.name,"") as lead_type'),
                    'vchr_enquiry_feedback',
                    'vchr_customer_email',
                    'vchr_customer_mobile',
                    'vchr_customer_company_name',
                    'et.vchr_enquiry_type as enquiry_type',
                    'pk_int_enquiry_id',
                    'tbl_enquiries.int_status',
                    'fk_int_purpose_id',
                    'vchr_purpose',
                    'fk_int_enquiry_type_id',
                    'vchr_color',
                    'feedback_status',
                    'tbl_users.vchr_user_name',
                    'tbl_enquiries.created_at as created_at',
                    'tbl_enquiries.updated_at',
                    'vchr_status',
                    'read_status',
                    'vchr_purpose',
                    'staff_id',
                    'lead_type_id',
                    'tbl_enquiries.next_follow_up'
                )
                ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                ->where(function ($query) use ($assignedStafId, $userData) {
                    if ($userData->int_role_id == Variables::STAFF && $userData->is_co_admin == 0) {
                        $query
                            ->where('tbl_enquiries.staff_id', $userData->pk_int_user_id)
                            ->orwhere('tbl_enquiries.created_by', $userData->pk_int_user_id)
                            ->orwhereIn('tbl_enquiries.staff_id', $assignedStafId);
                    }
                });
        }

        if ($userData->int_role_id == Variables::USER || $userData->is_co_admin == 1) {
            $enquiries->where('tbl_enquiries.fk_int_user_id', $vendorId);
        } elseif ($userData->int_role_id == Variables::STAFF) {
            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', $userData->designation_id)
                    ->where('menus.url', 'user/enquiriess')
                    ->exists()
            ) {
                // return 1;
                $enquiries
                    ->where('tbl_enquiries.staff_id', $userData->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', null)
                    ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                    ->get();
            }
            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', $userData->designation_id)
                    ->where('menus.url', 'user/enquiries')
                    ->exists()
            ) {
                $prvi = Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', $userData->designation_id)
                    ->where('menus.url', 'user/enquiries');
                if ($prvi === null) {
                    abort('403', 'UnAuthorized Action');
                }

                $enquiries
                    ->where('tbl_enquiries.staff_id', $userData->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', $userData->pk_int_user_id)
                    ->get();
            }

            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', $userData->designation_id)
                    ->where('menus.url', 'user/enquiries')
                    ->exists() and
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', $userData->designation_id)
                    ->where('menus.url', 'user/enquiriess')
                    ->exists()
            ) {
                // return 4;
                $enquiries
                    ->where('tbl_enquiries.staff_id', $userData->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', null)
                    ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                    ->get();
            }
        }
        // return 'ahgdcs';
        if ($request->has('enquiry_type') && $request->enquiry_type != "") {
            $enquiries->where('tbl_enquiries.fk_int_enquiry_type_id', $request->enquiry_type);
        }
        if ($request->has('enquiry_purpose') && $request->enquiry_purpose != "") {
            $enquiries->where('tbl_enquiries.fk_int_purpose_id', $request->enquiry_purpose);
        }
        if ($request->has('staff_id') && $request->staff_id != "") {
            $enquiries->where('tbl_enquiries.staff_id', $request->staff_id);
        }
        if ($request->has('enquiry_status') && $request->enquiry_status != "") {
            $enquiries->where('tbl_enquiries.feedback_status', $request->enquiry_status);
        }
        if ($request->has('created_at') && $request->created_at != "") {
            $enquiries
                ->whereYear('tbl_enquiries.created_at', $year)
                ->whereMonth('tbl_enquiries.created_at', $month)
                ->whereDay('tbl_enquiries.created_at', $day);
        }
        if ($request->has('created_by') && $request->created_by != "") {
            $enquiries->where('tbl_enquiries.created_by', $request->created_by);
        }
        if ($request->has('lead_type_id') && $request->filled('lead_type_id')) {
            $enquiries->where('tbl_enquiries.lead_type_id', $request->lead_type_id);
        }
        if ($request->has('filter') && $request->filter != "") {
            if ($request->filter == 'latest') {
                $enquiries->whereDate(
                    'tbl_enquiries.created_at',
                    '>=',
                    Carbon::today()
                        ->subDays(7)
                        ->toDateString()
                );
            } elseif ($request->filter == 'today') {
                $enquiries->whereDate('tbl_enquiries.created_at', Carbon::today()->toDateString());
            } elseif ($request->filter == 'current-month') {
                $enquiries->whereMonth('tbl_enquiries.created_at', '=', Carbon::today()->month);
            }
        }
        if ($request->has('additional_ids') && $request->has('additional_val')) {
            $add_ids = $request->additional_ids;
            $add_val = $request->additional_val;
            if (!empty($add_ids) && !empty($add_val)) {
                foreach ($add_ids as $inde => $add_id) {
                    $tbl_alias = 'tbl_' . $inde;
                    $enquiries
                        ->leftjoin('enquiry_additional_details as ' . $tbl_alias, $tbl_alias . '.enquiry_id', 'tbl_enquiries.pk_int_enquiry_id')
                        ->where($tbl_alias . '.field_id', $add_id)
                        ->where($tbl_alias . '.value', $add_val[$inde]);
                }
            }
        }

        // if ($userCount != NULL) {
        //     $enquiries->where('new_status', Enquiry::SHOW)->limit($userCount)->orderby('pk_int_enquiry_id', 'DESC');
        // } else {
        $enquiries->orderby('pk_int_enquiry_id', 'DESC');
        // }
        $enquiries = $enquiries
            ->where('followup_required', 1)
            ->get() //;//->take(100)

            ->sortByDesc(function ($query) {
                return $query->last_followup ? $query->last_followup->updated_at : $query->created_at;
            });
        $i = 1;
        foreach ($enquiries as $key => $row) {
            if (!empty($row->last_followup)) {
                if ($row->last_followup->log_type == EnquiryFollowup::TYPE_SCHEDULE && $row->last_followup->date < Carbon::today()) {
                    unset($enquiries[$key]);
                }
            }
            $row->slno = $i;
            $i++;
        }
        return Datatables::of($enquiries)
            ->addColumn('assign_agent', function ($enquiries) {
                return ' <div class="form-check form-check-assign" >
                                                                                    <input id="checkbox2" type="checkbox" data-enq_id="' .
                    $enquiries->pk_int_enquiry_id .
                    '">
                                                                                    
                                                                                    </div>';
            })
            ->editColumn('cust_name', function ($enquiries) {
                if ($enquiries->read_status == 0) {
                    if ($enquiries->vchr_customer_name != null) {
                        return '<span><a href="' .
                            'enquiry-timeline/' .
                            $enquiries->pk_int_enquiry_id .
                            '"><span class="d-tbl-noti">New</span>' .
                            'Name:' .
                            $enquiries->vchr_customer_name .
                            '<br>' .
                            'Company:' .
                            $enquiries->vchr_customer_company_name .
                            '</a></span>';
                    } else {
                        return '<span><a href="' .
                            'enquiry-timeline/' .
                            $enquiries->pk_int_enquiry_id .
                            '"><span class="d-tbl-noti">New</span>' .
                            "No name" .
                            '</a></span>';
                    }
                } else {
                    if ($enquiries->vchr_customer_name != null) {
                        return '<span><a href="' .
                            'enquiry-timeline/' .
                            $enquiries->pk_int_enquiry_id .
                            '">' .
                            'Name:' .
                            $enquiries->vchr_customer_name .
                            '<br>' .
                            'Company:' .
                            $enquiries->vchr_customer_company_name .
                            '</a></span>';
                    } else {
                        return '<span><a href="' . 'enquiry-timeline/' . $enquiries->pk_int_enquiry_id . '">' . 'No name' . '</a></span>';
                    }
                }
            })
            ->editColumn('cust_company', function ($enquiries) {
                if ($enquiries->vchr_customer_company_name != null) {
                    return '<span>' . $enquiries->vchr_customer_company_name . '</span>';
                } else {
                    return '<span>' . "No company name" . '</span>';
                }
            })
            ->editColumn('mobno', function ($enquiries) {
                if ($enquiries->vchr_customer_mobile != null) {
                    return $enquiries->vchr_customer_mobile;
                } else {
                    return "No mobile no";
                }
            })
            ->editColumn('enq_type', function ($enquiries) {
                if ($enquiries->fk_int_purpose_id != null) {
                    return '<span>' . $enquiries->vchr_purpose . '</span>';
                } else {
                    return '<span>' . "No Enquiry Purpose Added" . '</span>';
                }
            })
            ->editColumn('lead_type', function ($enquiries) {
                if ($enquiries->lead_type_id != null) {
                    return '<span>' . $enquiries->lead_type . '</span>';
                } else {
                    return '<span>' . "No Enquiry Type Added" . '</span>';
                }
            })
            ->editColumn('created_at', function ($enquiries) {
                if ($enquiries->created_at != null) {
                    return '<span>' . $enquiries->created_at . '</span>';
                } else {
                    return '<span>' . "No date Added" . '</span>';
                }
            })
            ->editColumn('feedback', function ($enquiries) {
                if ($enquiries->vchr_status != null) {
                    return '<span>' . $enquiries->vchr_status . '</span>';
                } else {
                    return '<span>' . "No enquiry status." . '</span>';
                }
            })
            ->editColumn('created', function ($enquiries) {
                if ($enquiries->vchr_user_name != null) {
                    return '<span>' . $enquiries->vchr_user_name . '</span>';
                } else {
                    return '<span>' . "No users." . '</span>';
                }
            })
            ->addColumn('show', function ($enquiries) {

                if ($enquiries->int_status == 1) {
                    return '                     
                                                                                                            <button enquiry-id="' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>
                                                                                                            <button id="delete_plan" enquiry-id="' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn btn-sm btn-danger mg-b-10 enquiry-delete"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                                                                                            <a href="' .
                        'template-whatsapp/' .
                        $enquiries->pk_int_enquiry_id .
                        '" target="_blank" class="btn  btn-sm btn-success "  data-toggle="tooltip" title="whatsapp" > <i class="fa fa-whatsapp"></i></a>
                                                                                                            <a target="_blank" href="' .
                        'enquiry-sms' .
                        '/' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn  btn-sm btn-info "  data-toggle="tooltip" title="sms" > <i class="fa fa-commenting-o"></i>
                                                                                                            
                                                                                                            </a>
                                                                                                            
                                                                                                            ';
                } //If Status is not in active state
                else {
                    return '
                                                                                                            <button enquiry-id="' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn btn-sm btn-primary mg-b-10 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal"> <i class="fa fa-edit mg-r-5"></i></button>
                                                                                                            <button id="delete_plan" enquiry-id="' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn btn-sm btn-danger mg-b-10 enquiry-delete"> <i class="fa fa-trash-o mg-r-5"></i></button>
                                                                                                            <a href="' .
                        'template-whatsapp/' .
                        $enquiries->pk_int_enquiry_id .
                        '" target="_blank" class="btn  btn-sm btn-success "  data-toggle="tooltip" title="whatsapp"> <i class="fa fa-whatsapp"></i></a>
                                                                                                            <a target="_blank" href="' .
                        'enquiry-sms/' .
                        $enquiries->pk_int_enquiry_id .
                        '" class="btn btn-sm btn-info"  data-toggle="tooltip" title="sms" > <i class="fa fa-commenting-o"></i></a>
                                                                                                            ';
                }
            })
            ->addColumn('assigned_to', function ($enquiry) {
                $assignedToUser = User::where('pk_int_user_id', $enquiry->staff_id)->first();

                if ($assignedToUser == null) {
                    $assignedTo = "Assign to Agent";
                } else {
                    $assignedTo = $assignedToUser->vchr_user_name;
                }
                return '<span><a href="javascript:showEditModal(' . $enquiry->pk_int_enquiry_id . ')">' . $assignedTo . '</a></span>';
            })
            ->addColumn('next_follow_up', function ($enquiry) {
                if ($enquiry->next_follow_up == null) {
                    return '<span><a href="javascript:showNextFollowUpModal(' .
                        $enquiry->pk_int_enquiry_id .
                        ')">Add follow up</a><a>On&nbsp;' .
                        $enquiry->scheduled_date .
                        '</a></span>';
                } else {
                    $total_value = explode(" ", $enquiry->next_follow_up);
                    if (count($total_value) == 3) {
                        $date = $total_value[0];
                        $time = $total_value[1];
                        $a = $total_value[2];

                        return '<span style="co><a>On&nbsp;' . $date . '&nbsp;at&nbsp;' . $time . '&nbsp;' . $a . '</a></span>';
                    } else {
                        $date = $total_value[0];

                        return '<span style="co><a>On&nbsp;' . $date . '</a></span>';
                    }
                }
            })
            // <a target="_blank" href="' . 'enquiry-proposal' . '/' . $enquiries->pk_int_enquiry_id . '" class="btn  btn-sm btn-primary "  data-toggle="tooltip" title="proposal" > <i class="fas fa-sticky-note"></i>

            //     </a>
            ->rawColumns(['next_follow_up', 'show', 'cust_name', 'created_at', 'created', 'enq_type', 'feedback', 'lead_type', 'assigned_to', 'assign_agent'])
            ->toJson(true);
    }


    /**
     * d
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $ranks = [1, 2, 3, 4, 5];
        $userId = User::getVendorId();
        $types = EnquiryType::OnlyUser()
            ->where('int_status', '1')
            ->where(function ($where) use ($userId) {
                $where->where('vendor_id', $userId)->orWhere('vendor_id', 0);
            })
            ->orderBy('vchr_enquiry_type')->get();
        $staffId = User::active()->where('int_role_id', User::STAFF)
            ->where('parent_user_id', $userId)
            ->orWhere('pk_int_user_id', $userId)
            ->get();
        $countrys = Country::All();
        $ip = Common::getIp();
        $data = \Location::get($ip);
        if ($data) {
            $countryCodeIp = $data->countryCode;
            $Code = Country::where('country_code', $countryCodeIp)->first();
            if ($Code) {
                $countryCode = $Code->code;
            } else {
                $countryCode = "IN";
            }
        } else {
            $countryCode = "IN";
        }
        $purposes = EnquiryPurpose::where('fk_int_user_id', $userId)->get();
        $enquiry_status = FeedbackStatus::where('fk_int_user_id', $userId)->get();
        $additional_fields = LeadAdditionalField::where('vendor_id', $userId)
            ->with('additionalPurpose')
            ->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }
        $district = District::where('vendor_id', $userId)
            ->where('status', 1)
            ->get();
        $taluk = Taluk::where('vendor_id', $userId)
            ->where('status', 1)
            ->get();

        $fields = EnquiryFieldCustomise::where('vendor_id', $userId)->first();
        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $agencies = Agency::where('vendor_id', $userId)->select('id', 'name')->get();
        if (Auth::user()->int_role_id == User::USERS) {
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::active()->where('parent_user_id', Auth::user()->pk_int_user_id)->get();
            return View(
                'backend.pages.enquiries.create',
                compact('active_fields', 'required_fields', 'types', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'additional_fields', 'district', 'taluk', 'ranks', 'agencies')
            );
        } elseif (Auth::user()->int_role_id == User::STAFF) {
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::active()->where('parent_user_id', Auth::user()->parent_user_id)->get();
            return View(
                'backend.pages.enquiries.create',
                compact('active_fields', 'required_fields', 'types', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'additional_fields', 'district', 'taluk', 'ranks', 'agencies')
            );
        } else {
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::active()->where('parent_user_id', Auth::user()->parent_user_id)->get();
            return View(
                'backend.pages.enquiries.create-enquiry',
                compact('active_fields', 'required_fields', 'types', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'district', 'taluk', 'ranks')
            );
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        Log::info('Enquiry store request from web', [
            'request' => $request->all(),
        ]);

        $input = $request->all();
        $vendorId = User::getVendorId();
        $input['fk_int_user_id'] = $vendorId;
        if ($vendorId == Variables::APOLO_USER_ID) {
            $rules = [
                'fk_int_enquiry_type_id' => 'required',
                'vchr_customer_mobile' => 'required|digits_between:6,14|unique:tbl_users,mobile_no',
                'landline_number' => 'digits_between:0,30',
                'feedback_status' => 'required',
                'lead_type_id' => 'required',
                'purchase_date' => 'required',
                'function_date' => 'required',
                'exp_wt_grams' => 'required',
            ];
            $rulesMessage = [
                'fk_int_enquiry_type_id.required' => 'Enquiry Source is required.',
                'feedback_status.required' => 'Enquiry status is required.',
                'vchr_customer_mobile.required' => 'Mobile is required.',
                'vchr_customer_mobile.unique' => 'Lead already exist with same mobile number.',
                'vchr_customer_mobile.digits_between' => 'Mobile number must be between 6 to 14 digits',
                'landline_number.digits_between' => "Landline number must be digits",
                'lead_type_id.required' => 'Lead Type  required.',
                //  'lead_type_id.numeric' => 'Lead Type  required.',
                'purchase_date.required' => 'Purchase  Date  required.',
                'function_date.required' => 'Function  Date  required.',
                'exp_wt_grams.required' => 'Expected weight    required.',
            ];
            $validator = Validator::make($request->all(), $rules, $rulesMessage);
        } else if ($vendorId == Variables::FORTUNE_USER_ID) {
            $rules = [
                'fk_int_enquiry_type_id' => 'required',
                'vchr_customer_mobile' => 'required|digits_between:6,14|unique:tbl_enquiries,mobile_no',
            ];
            $rulesMessage = [
                'fk_int_enquiry_type_id.required' => 'Enquiry Source is required.',
                'vchr_customer_mobile.required' => 'Mobile is required.',
                'vchr_customer_mobile.unique' => 'Lead already exist with same mobile number.',
                'vchr_customer_mobile.digits_between' => 'Mobile number must be between 6 to 14 digits',
            ];
            $validator = Validator::make($request->all(), $rules, $rulesMessage);
        } else {
            $validator = Validator::make($request->all(), Enquiry::$rules, Enquiry::$rulesMessage);
        }

        // $validator = validator($input, Enquiry::$rules, Enquiry::$rulesMessage);
        if ($validator->passes()) {
            $request->merge([
                'country_code' => str_replace("+", "", $request->country_code)
            ]);

            $exist_mobile = Enquiry::where('fk_int_user_id', $vendorId)
                ->where(function ($where) use ($request) {
                    $where->where('vchr_customer_mobile', $request->country_code . $request->vchr_customer_mobile)
                        ->orWhere('vchr_customer_mobile', "+" . $request->country_code . $request->vchr_customer_mobile)
                        ->orWhere('more_phone_numbers', 'like', '%' . $request->vchr_customer_mobile . '%');
                })
                ->first();
            if ($exist_mobile) {
                Flash::error("Whoops! Form Validation Failed ");

                return redirect()
                    ->back()
                    ->withErrors(['vchr_customer_mobile' => ["This mobile number or additional numbers already exists."]])
                    ->withInput();
            }

            $statusId = $request->feedback_status;
            if (!$request->feedback_status) {
                $feedback_status = FeedbackStatus::where('vchr_status', 'New')
                    ->where('fk_int_user_id', $vendorId)
                    ->first();
                if (!$feedback_status) {
                    $feedback_status = new FeedbackStatus();
                    $feedback_status->vchr_status = 'New';
                    $feedback_status->vchr_color = '#000000';
                    $feedback_status->fk_int_user_id = $vendorId;
                    $feedback_status->created_by = $vendorId;
                    $feedback_status->save();
                }
                $statusId = $feedback_status->pk_int_feedback_status_id;
            }

            try {
                $enquiry = new Enquiry();
                $enquiry->fill($input);
                $enquiry->taluk_id = $request->taluk_id;
                $enquiry->district_id = $request->district_id;
                $enquiry->model_id = $request->model_id;
                $enquiry->purchase_plan = $request->purchase_plan;
                $enquiry->date_of_purchase = $request->date_of_purchase;
                $enquiry->live_deal = $request->live_deal;
                $enquiry->remarks = $request->remarks;
                $enquiry->competing_model = $request->competing_model;
                $enquiry->vchr_customer_name = $request->vchr_customer_name;
                $enquiry->vchr_customer_company_name = $request->vchr_customer_company_name;
                $enquiry->vchr_customer_mobile = $request->country_code . $request->vchr_customer_mobile;
                $enquiry->vchr_customer_email = $request->vchr_customer_email;
                $enquiry->mobile_no = $request->vchr_customer_mobile;
                $enquiry->vchr_enquiry_feedback = $request->vchr_enquiry_feedback;
                $enquiry->country_code = $request->country_code;
                $enquiry->lead_type_id = $request->lead_type_id;
                $enquiry->purchase_date = $request->purchase_date;
                $enquiry->exp_wt_grams = $request->exp_wt_grams;
                $enquiry->function_date = $request->function_date;
                $enquiry->agency_id = $request->agency_id;

                if ($request->staff_id) {
                    $enquiry->staff_id = $request->staff_id;
                }

                $enquiry->landline_number = $request->landline_number;
                $enquiry->designation_id = $request->designation_id;
                if ($request->more_phone_numbers) {
                    $phone = array();
                    foreach ($request->more_phone_numbers as $key => $number) {

                        if ($number && strlen((string)$number) > 3) {
                            $phone[$key] = $request->more_country_code[$key] . $number;
                        }
                    }
                    $more_phone_numbers = json_encode($phone);
                    $enquiry->more_phone_numbers = $more_phone_numbers;
                    // $request->more_phone_numbers = array_filter($request->more_phone_numbers);
                    // $more_phone_numbers = json_encode($request->more_phone_numbers);
                }
                $enquiry->created_by = Auth::user()->pk_int_user_id;
                $enquiry->date_of_birth = $request->date_of_birth;
                $enquiry->feedback_status = $statusId;
                $enquiry->read_status = 0;
                $enquiry->fk_int_enquiry_type_id = $request->fk_int_enquiry_type_id;
                $enquiry->fk_int_purpose_id = $request->fk_int_purpose_id;
                $enquiry->event_date = $request->event_date;
                $enquiry->address = $request->address;
                $flag = $enquiry->save();
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
                Flash::error("Whoops! Form Validation Failed ");
                return redirect()
                    ->back()
                    ->withInput();
            }

            if ($request->additional_field) {
                foreach ($request->additional_field as $index => $value) {
                    if ($value != "") {
                        $field_name = LeadAdditionalField::find($index);
                        if ($field_name->type_text == 'Image') {
                            $additionalDetails = new LeadAdditionalDetails();
                            $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                            $additionalDetails->field_id = $index;
                            $additionalDetails->field_name = $field_name->field_name;
                            $filename = $value->store('public/custom_field_image');
                            $additionalDetails->value = $filename;
                            $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                            $additionalDetails->save();
                        } elseif ($field_name->input_type == 8) {
                            // Multi Select DropDown
                            $additionalDetails = new LeadAdditionalDetails();
                            $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                            $additionalDetails->field_id = $index;
                            $additionalDetails->field_name = $field_name->field_name;
                            $additionalDetails->value = json_encode($value);
                            $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                            $additionalDetails->save();
                        } else {
                            $additionalDetails = new LeadAdditionalDetails();
                            $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                            $additionalDetails->field_id = $index;
                            $additionalDetails->field_name = $field_name->field_name;
                            $additionalDetails->value = $value;
                            $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                            $additionalDetails->save();
                        }
                    }
                }
            }

            try {
                if (!$request->staff_id)
                     AutomationFacade::newLeadFunctions($enquiry->pk_int_enquiry_id);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            $vendor = User::find($vendorId);
            $enquiryType = EnquiryType::where('pk_int_enquiry_type_id', $request->fk_int_enquiry_type_id)->first()->vchr_enquiry_type;
            if (Auth::user()->int_role_id == User::STAFF) {
                $vendor->notify(new NewLead($enquiry));
            }

            if ($request->lead_type_id) {
                $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                if ($lead_type && $lead_type->name == 'Key Person') {
                    $exist = EnquiryType::where('vendor_id', $vendorId)
                        ->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)
                        ->first();
                    if (!$exist) {
                        $enquiry_source = new EnquiryType();
                        $enquiry_source->vendor_id = $vendorId;
                        $enquiry_source->vchr_enquiry_type = $enquiry->vchr_customer_name;
                        $enquiry_source->fk_int_user_id = $vendorId;
                        $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                        $enquiry_source->int_status = Variables::ACTIVE;
                        $enquiry_source->created_by = Auth::user()->pk_int_user_id;
                        $enquiry_source->save();
                    }
                }
            }
            try {
                event(new CreateFollowup($enquiry->fk_int_enquiry_type_id, EnquiryFollowup::TYPE_NEW, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));

                if ($request->feedback_status) {
                    event(new CreateFollowup($request->feedback_status, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                }
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            if ($request->vchr_enquiry_feedback) {
                try {
                    event(new CreateFollowup($request->vchr_enquiry_feedback, EnquiryFollowup::TYPE_NOTE, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }
            }
            // Start Add Followup
            if ($request->followup_date) {
                if ($request->followup_date != " " && $request->followup_time != "") {
                    $date_time = Carbon::createFromFormat('d-m-Y', $request->followup_date)->toDateString() . ' ' . Carbon::createFromFormat('H:i A', $request->followup_time)->toTimeString();
                } elseif ($request->followup_date != " ") {
                    $date_time = Carbon::createFromFormat('d-m-Y', $request->followup_date)->toDateString() . ' ' . Carbon::now()->toTimeString();
                }
                $input = [
                    'name' => request('followup_title') != '' ? request('followup_title') : 'Follow Up',
                    'description' => request('followup_title') != '' ? request('followup_title') : 'Follow Up created from lead creation',
                    'scheduled_date' => $date_time ?? now(),
                    'task_category_id' => 2,
                    'assigned_to' => $enquiry->staff_id,
                    'assigned_by' => Auth::user()->pk_int_user_id,
                    'vendor_id' => $vendorId,
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'status' => 0,
                ];
                Task::create($input);
            }
            // End Add Followup

            if ($flag) {
                $show = new Common();

                /**-------------------AUTOMATION_START------------------------------------**/
                /**--WEBHOOK---------------------------------------------**/
                $automation_rule_webhook = $show->getRule($vendorId, 'new_lead', 'webhook', $enquiry->fk_int_enquiry_type_id);
                $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->feedback_status)->select('vchr_status')->first();
                $post_data = [
                    'customer_name' => $enquiry->vchr_customer_name,
                    'email' => $enquiry->vchr_customer_email,
                    'status' => ($status) ? $status->vchr_status : "New Status",
                    'phone' => $enquiry->vchr_customer_mobile,
                    'mobile' => $enquiry->mobile_no,
                    'flag' => "new_lead",
                ];
                if (count($automation_rule_webhook) > 0) {
                    foreach ($automation_rule_webhook as $w_hook) {
                        if ($w_hook->webhook_id != null) {
                            try {
                                $webHook = $show->getWebHookById($w_hook->webhook_id);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }

                            if ($webHook) {
                                try {
                                    $show->postToWebHook($webHook->url, $post_data);
                                } catch (\Exception $e) {
                                    \Log::info($e->getMessage());
                                }
                            }
                        }
                    }
                }
                /**--WEBHOOK---------------------------------------------**/
                /**--TASK---------------------------------------------**/
                $automation_rule_task = $show->getRule($vendorId, 'new_lead', 'task');
                if ($automation_rule_task) {
                    $input = [
                        'name' => $automation_rule_task->task_title,
                        'description' => $automation_rule_task->task_description,
                        'scheduled_date' => Carbon::tomorrow(),
                        'task_category_id' => $automation_rule_task->task_category_id,
                        'assigned_to' => $automation_rule_task->task_assigned_to,
                        'assigned_by' => $vendorId,
                        'vendor_id' => $vendorId,
                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                        'status' => 0,
                    ];
                    Task::create($input);
                }
                /**-------------------AUTOMATION_END------------------------------------**/

                /**--WHATSAPP---------------------------------------------**/
                $automation_whatsapp = AutomationRule::where('vendor_id', $enquiry->fk_int_user_id)
                    ->where('trigger', 'new_lead')
                    ->where('action', 'whatsapp')
                    ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                    ->orderBy('id', 'DESC')
                    ->first();

                if ($automation_whatsapp) {
                    $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                        ->select('text_whatsapp_template_description')
                        ->first();
                    if ($whatsappTemplate) {
                        $gupshupObj = new GupShup();
                        $credientails = WatsappCredential::where('vendor_id', $enquiry->fk_int_user_id)
                            ->where('status', 1)
                            ->where('platform_id', 2)
                            ->first();
                        if ($credientails) {
                            $data = [
                                "api_key" => $credientails->access_key,
                                "from_number" => $credientails->source_mobile_num,
                                "app_name" => $credientails->template_name
                            ];
                            $gupshupObj->sendWatsappMessageIndividal(
                                $enquiry->country_id ?? '',
                                $enquiry->vchr_customer_mobile,
                                str_replace("{{name}}", $enquiry->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description),
                                $data
                            );
                        }
                    }
                }
                /**--WHATSAPP---------------------------------------------**/

                /**--Assigned to Campaign--------------------------------------------- */
                $automation_rule_campaign = $show->getRule($vendorId, 'new_lead', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                    if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                        try {
                            $leads = Enquiry::find($enquiry->pk_int_enquiry_id);
                            $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                            $show->addToCampaign($automation_rule_campaign, $leads, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                }

                $enquiryNotif = Enquiry::find($enquiry->pk_int_enquiry_id);
                if ($enquiryNotif->staff_id != null) {
                    $staff = User::find($enquiryNotif->staff_id);
                    if ($staff && $enquiryNotif->staff_id != User::getVendorId()) {
                        $vendor->notify(new AssignLeadToStaff($enquiryNotif, Auth::user()->pk_int_user_id));
                    }
                    if ($staff) {
                        $staff->notify(new AssignLeadToStaff($enquiryNotif, Auth::user()->pk_int_user_id));
                        event(new LeadAssigned($enquiry->pk_int_enquiry_id, $request->staff_id));
                    }
                }

                /**--API---------------------------------------------**/
                $source = $enquiry->fk_int_enquiry_type_id;
                $automation_rule_api = $show->getRule($vendorId, 'new_lead', 'api', $source);
                if ($automation_rule_api && $automation_rule_api->api != null) {
                    try {
                        if ($vendorId == Variables::NIKSHAN_USER_ID) {
                            // Nikshan automation
                            $usr = $enquiryNotif->assigned_user;
                            $extension = null;
                            if ($usr) {
                                $extension = $usr->userExtension ? $usr->userExtension->extension : null;
                            }
                            if ($extension)
                                $show->postToIvrAutoCall($automation_rule_api->api, $extension, $post_data);
                        } else {
                            $show->postToWebHook($automation_rule_api->api, $post_data);
                        }
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
                /**--API---------------------------------------------**/

                /**-- Assigned to Campaign ------------------------------------------- */
                // $show->showCrmUsersSubscription($vendorId);
                $userObject = User::getUserDetails($vendorId);
                $userAdminObject = User::getSingleAdminDetails();
                //Notifications
                $notifications = new Notifications();
                $from = env('MAIL_FROM_ADDRESS');
                $to = $userObject->email;
                $subject = "New Leads Notifications";
                $name = $userObject->vchr_user_name;
                $logo = $userAdminObject->vchr_logo;
                $attachment = "";
                $telegramId = $userObject->telegram_id;
                $mobileNumber = $userObject->vchr_user_mobile;
                $defaultSenderIdAdmin = SingleSMS::getSenderid($userAdminObject->pk_int_user_id, '');
                $defaultRouteAdmin = SingleSMS::getRoute($userAdminObject->pk_int_user_id, '');
                $content1 = 'New Leads via  ' . $enquiryType . '-' . $request->vchr_customer_name . '( ' . $request->vchr_customer_mobile . ' ). Added By ' . auth()->user()->vchr_user_name;
                $content2 = 'You have new leads via ' . $enquiryType . '-' . $request->vchr_customer_name . '( ' . $request->vchr_customer_mobile . ' ). Added By ' . auth()->user()->vchr_user_name;
                $dataSend['message'] = $content1;
                $dataSend['user_id'] = $enquiryNotif->staff_id ?? $vendorId;
                $dataSend['page'] = 'lead_page';
                $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin, $dataSend);
                //-----------------End Notification

                if (!$enquiryNotif->staff_id && !$automation_rule_campaign) {
                    /**--Assigned to Datapool--------------------------------------------- */
                    $automation_rule_campaign = $show->getRule($vendorId, 'new_lead', 'add_to_data_pool', $enquiryNotif->fk_int_enquiry_type_id);

                    if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                        if ($automation_rule_campaign->enquiry_source_id == $enquiryNotif->fk_int_enquiry_type_id) {
                            try {
                                $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                                $show->addToCampaign($automation_rule_campaign, $enquiryNotif, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    } else {
                        $enquiryNotif->staff_id = auth()->user()->pk_int_user_id;
                        $enquiryNotif->save();
                    }
                }

                if ($vendorId == 880) {
                    if (Auth::user()->int_role_id == User::ADMIN) {
                        Flash::success("New Lead Created Successfully");

                        return redirect('admin/enquiries/create');
                    } else {
                        Flash::success("New Lead Created Successfully");

                        return redirect('user/enquiries/create');
                    }
                } else {
                    if (Auth::user()->int_role_id == User::ADMIN) {
                        Flash::success("New Lead Created Successfully");
                        return redirect('/user/enquiries');
                    } else {
                        Flash::success("New Lead Created Successfully");
                        return redirect('/user/enquiries');
                    }
                }
            }
        } else {
            Flash::error("Whoops! Form Validation Failed ");

            return redirect()
                ->back()
                ->withErrors($validator)
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\BackendModel\Enquiry $enquiry
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $enquiry = Enquiry::leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->where('pk_int_enquiry_id', $id)
            ->select(
                'tbl_enquiries.fk_int_user_id',
                'vchr_customer_name',
                'tbl_enquiries.designation_id',
                'date_of_birth',
                'vchr_enquiry_feedback',
                'vchr_customer_email',
                'vchr_customer_mobile',
                'vchr_customer_company_name',
                'et.vchr_enquiry_type as enquiry_type',
                'pk_int_enquiry_id',
                'fk_int_purpose_id',
                'fk_int_enquiry_type_id',
                'feedback_status',
                'vchr_status',
                'landline_number',
                'more_phone_numbers',
                'country_code',
                'staff_id',
                'tbl_enquiry_purpose.vchr_purpose',
                'mobile_no',
                'address',
                'lead_type_id',
                'purchase_date',
                'exp_wt_grams',
                'function_date',
                'district_id',
                'taluk_id',
                'competing_model',
                'remarks',
                'live_deal',
                'date_of_purchase',
                'purchase_plan',
                'model_id',
                'agency_id'
            )
            ->first();
        $enquiry->more_phone_numbers = json_decode($enquiry->more_phone_numbers);

        $additional_details = LeadAdditionalField::where('vendor_id', User::getVendorId())
            ->withCount('additionalPurpose')
            ->get();
        $enquiry->additional_details = $additional_details;
        foreach ($additional_details as $key => $additional_field) {
            if ($additional_field->input_type == 2 || $additional_field->input_type == 8) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            $enq_details = LeadAdditionalDetails::where('enquiry_id', $id)
                ->where('field_id', $additional_field->id)
                ->where('field_name', $additional_field->field_name)
                ->first();
            if ($enq_details) {
                $additional_field->info = $enq_details->value;
            } else {
                $additional_field->info = '';
            }
        }
        if ($enquiry) {
            return response()->json(['msg' => "Enquiry detail Found.", 'status' => 'success', 'data' => $enquiry]);
        } else {
            return response()->json(['msg' => "Enquiry detail not found.", 'status' => 'fail']);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\BackendModel\Enquiry $enquiry
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        Log::info('Update enquiry request received from web', [
            'request' => $request->all()
        ]);

        $enquiry = Enquiry::query()->find($id);
        $old_agent_id = $enquiry->staff_id;
        $vendorId = User::getVendorId();
        if ($enquiry instanceof Enquiry) {
            $input = $request->except('_method');
            $rulesUpdate = Enquiry::$rulesUpdate;
            $validator = validator($input, $rulesUpdate, Enquiry::$rulesMessageUpdate);

            $leadExists = Enquiry::query()
                ->where('fk_int_user_id', $vendorId)
                ->where([
                    'country_code' => $request->integer('country_code'),
                    'mobile_no' => $request->get('vchr_customer_mobile'),
                ])
                ->whereKeyNot($id)
                ->exists();
            if ($leadExists) {
                Flash::error("Whoops! Form Validation Failed ");
                return response()->json(['msg' => "Lead already exist with same mobile number", 'status' => 'fail']);
            }

            if ($validator->passes()) {
                $enquiryAttributes = array_keys($enquiry->getAttributes());

                $filteredInput = array_filter(
                    $input,
                    function ($key) use ($enquiryAttributes) {
                        return in_array($key, $enquiryAttributes);
                    },
                    ARRAY_FILTER_USE_KEY
                );

                $vendor = User::query()->find($vendorId);
                $old_note = $enquiry->vchr_enquiry_feedback;
                $old_source_id = $enquiry->fk_int_enquiry_type_id;
                $old_status_id = $enquiry->feedback_status;
                $enquiry->fill($filteredInput);
                $enquiry->mobile_no = $request->vchr_customer_mobile;
                $enquiry->district_id = $request->district_id;
                $enquiry->taluk_id = $request->taluk_id;
                $enquiry->model_id = $request->model_id;
                $enquiry->purchase_plan = $request->purchase_plan;
                $enquiry->date_of_purchase = $request->date_of_purchase;
                $enquiry->live_deal = $request->live_deal;
                $enquiry->remarks = $request->remarks;
                $enquiry->competing_model = $request->competing_model;
                $enquiry->vchr_customer_mobile = $request->country_code . $request->vchr_customer_mobile;
                $enquiry->updated_by = Auth::user()->pk_int_user_id;
                $enquiry->read_status = 1;
                $enquiry->address = $request->address;
                $enquiry->function_date = $request->function_date;
                $enquiry->lead_type_id = $request->lead_type_id;
                $enquiry->agency_id = $request->agency_id;
                $enquiry->fk_int_purpose_id = $request->fk_int_purpose_id;
                if ($request->more_phone_numbers) {
                    $phone = array();
                    foreach ($request->more_phone_numbers as $key => $num) {
                        if ($num && strlen((string)$num) > 3) {
                            $phone[$key] = $request->more_country_code[$key] . $num;
                        }
                    }
                    $phone = array_values($phone);
                    $more_phone_numbers = json_encode($phone, true);
                    $enquiry->more_phone_numbers = $more_phone_numbers;
                } else {
                    $enquiry->more_phone_numbers = null;
                }
                $flag = $enquiry->save();

                if ($request->staff_id != null) {
                    if ($old_agent_id != null) {
                        if ($old_agent_id != $request->staff_id) {
                            $staff = User::find($request->staff_id);
                            $enquiry->assigned_date = Carbon::today();
                            $enquiry->save();
                            $vendor->notify(new AssignLeadToStaff($enquiry, Auth::user()->pk_int_user_id));
                            $staff->notify(new AssignLeadToStaff($enquiry, Auth::user()->pk_int_user_id));
                            event(new LeadAssigned($enquiry->pk_int_enquiry_id, $request->staff_id));
                            try {
                                $note = $staff->vchr_user_name . " has been designated as the lead";
                                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }

                if ($flag) {
                    try {
                        event(new CreateFollowup("Lead details updated", EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }

                if ($old_note != $request->vchr_enquiry_feedback) {
                    try {
                        $limelineLog = event(new CreateFollowup($request->vchr_enquiry_feedback, EnquiryFollowup::TYPE_NOTE, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
                if ($enquiry->isDirty('fk_int_purpose_id')) {
                    Log::info('Running purpose change automation when updating enquiry', [
                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                        'old_purpose_id' => $enquiry->getOriginal('fk_int_purpose_id'),
                        'new_purpose_id' => $enquiry->fk_int_purpose_id,
                    ]);
                    Enquiry::handlePurposeChangeAutomation($enquiry);
                }
                if ($old_source_id != $enquiry->fk_int_enquiry_type_id) {
                    $assignSourceData = AutomationRule::where('trigger', 'source_change')
                        ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                        ->where('vendor_id', $enquiry->fk_int_user_id)
                        ->where('action', 'assign')
                        ->orderby('id', 'DESC')
                        ->first();
                    if ($assignSourceData) {
                        AutomationRule::autoassign($assignSourceData, $enquiry->pk_int_enquiry_id);
                    }
                    $automation_rule_task = AutomationRule::query()
                        ->where('trigger', 'source_change')
                        ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                        ->where('vendor_id', $enquiry->fk_int_user_id)
                        ->where('action', 'task')
                        ->orderby('id', 'DESC')
                        ->first();
                    if ($automation_rule_task) {
                        if ($automation_rule_task->duration) {
                            $scheduled_date = Carbon::now()->addMinutes($automation_rule_task->duration);
                        } else {
                            $scheduled_date = Carbon::now();
                        }

                        Task::create([
                            'name' => $automation_rule_task->task_title,
                            'description' => $automation_rule_task->task_description,
                            'scheduled_date' => $scheduled_date ?? now(),
                            'task_category_id' => $automation_rule_task->task_category_id,
                            'assigned_to' => $automation_rule_task->task_assigned_to,
                            'assigned_by' => $enquiry->fk_int_user_id,
                            'vendor_id' => $enquiry->fk_int_user_id,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'status' => 0,
                        ]);
                    }
                    $show = new Common;
                    $automation_rule_campaign = $show->getRule($vendorId, 'source_change', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

                    if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                        if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                            try {
                                $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                                $show->addToCampaign($automation_rule_campaign, $enquiry, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }


                }
                if ($old_status_id != $enquiry->feedback_status) {
                    $commonObj = new Common();
                    $automation_rule_task = $commonObj->getRuleStatusChange($vendorId, 'status_change', 'task', $enquiry->feedback_status);
                    if ($automation_rule_task) {
                        Task::create([
                            'name' => $automation_rule_task->task_title,
                            'description' => $automation_rule_task->task_description,
                            'scheduled_date' => Carbon::tomorrow(),
                            'task_category_id' => $automation_rule_task->task_category_id,
                            'assigned_to' => $automation_rule_task->task_assigned_to,
                            'assigned_by' => $vendorId,
                            'vendor_id' => $vendorId,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'status' => 0,
                        ]);
                    }

                    Enquiry::statusChangeFunction($enquiry);
                }

                if ($request->additional_field) {
                    foreach ($request->additional_field as $index => $value) {
                        if ($value != "") {
                            $field_name = LeadAdditionalField::find($index);
                            $exists_field = LeadAdditionalDetails::where('field_id', $field_name->id)
                                ->where('field_name', $field_name->field_name)
                                ->where('enquiry_id', $id)
                                ->first();
                            if ($exists_field) {
                                if ($field_name->type_text == 'Image') {
                                    $filename = $value->store('public/custom_field_image');
                                    $exists_field->value = $filename;
                                } elseif ($field_name->input_type == 8) {
                                    // Multi Select DropDown
                                    $exists_field->value = json_encode($value);
                                } else {
                                    $exists_field->value = $value;
                                }

                                $exists_field->save();

                                //  Code for marble gallary
                                if ($field_name->field_name == MarbleGallery::LEAD_COMPLETE && $vendorId == MarbleGallery::MARBLE_GALLERY_ID) {
                                    if ($value == 'Yes') {
                                        $this->changeAttandanceStatusAndPushToPOS($enquiry, Auth::user()->int_role_id);
                                    }
                                }
                            } else {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                $additionalDetails->field_id = $index;
                                $additionalDetails->field_name = $field_name->field_name;
                                if ($field_name->type_text == 'Image') {
                                    $filename = $value->store('public/custom_field_image');
                                    $additionalDetails->value = $filename;
                                } elseif ($field_name->input_type == 8) {
                                    // Multi Select DropDown
                                    $additionalDetails->value = json_encode($value);
                                } else {
                                    $additionalDetails->value = $value;
                                }
                                $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                                $additionalDetails->save();

                                //  Code for marble gallary
                                if ($field_name->field_name == MarbleGallery::LEAD_COMPLETE && $vendorId == MarbleGallery::MARBLE_GALLERY_ID) {
                                    if ($value == 'Yes') {
                                        $this->changeAttandanceStatusAndPushToPOS($enquiry, Auth::user()->int_role_id);
                                    }
                                }
                            }
                        }
                    }
                }

                if ($request->lead_type_id) {
                    $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                    if ($lead_type && $lead_type->name == 'Key Person') {
                        $exist = EnquiryType::where('vendor_id', $vendorId)
                            ->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)
                            ->first();
                        if (!$exist) {
                            $enquiry_source = new EnquiryType();
                            $enquiry_source->vendor_id = $vendorId;
                            $enquiry_source->vchr_enquiry_type = $enquiry->vchr_customer_name;
                            $enquiry_source->fk_int_user_id = $vendorId;
                            $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                            $enquiry_source->int_status = Variables::ACTIVE;
                            $enquiry_source->created_by = Auth::user()->pk_int_user_id;
                            $enquiry_source->save();
                        }
                    }
                }
                $commonObj = new Common();
                /**------------------------AUTOMATION_START--------------------------------------**/
                if ($request->feedback_status != $old_status_id) {
                    try {
                        event(new CreateFollowup($request->feedback_status, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id, $old_status_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }

                    $automation_rule = AutomationRule::where('vendor_id', $vendorId)
                        ->where('trigger', 'status_change')
                        ->where('action', 'webhook')
                        ->where('feedback_status_id', $enquiry->feedback_status)
                        ->orderBy('id', 'DESC')
                        ->get();
                    $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->feedback_status)
                        ->select('vchr_status')
                        ->first();

                    $post_data = [
                        'customer_name' => $enquiry->vchr_customer_name,
                        'email' => $enquiry->vchr_customer_email,
                        'status' => $status->vchr_status ?? null,
                        'phone' => $enquiry->vchr_customer_mobile,
                        'mobile' => $enquiry->mobile_no,
                        'flag' => "status_change",
                        'state' => ($vendorId == 1119) ? $enquiry->additional_details->where('field_id', '=', 317 /* state id is for bazani */)->value('value') ?? null : null
                    ];
                    if (count($automation_rule) > 0) {
                        foreach ($automation_rule as $w_hook) {
                            if ($w_hook->webhook_id != null && $w_hook->feedback_status_id == $request->feedback_status) {
                                try {
                                    $webHook = $commonObj->getWebHookById($w_hook->webhook_id);
                                } catch (\Exception $th) {
                                    \Log::info($e->getMessage());
                                }
                                if ($webHook) {
                                    try {
                                        try {
                                            $commonObj->postToWebHook($webHook->url, $post_data);
                                        } catch (\Exception $e) {
                                            \Log::info($e->getMessage());
                                        }
                                    } catch (\Exception $e) {
                                        \Log::info($e->getMessage());
                                    }
                                }
                            }
                        }
                    }


                    $automation_rule_api = AutomationRule::where('vendor_id', $vendorId)
                        ->where('trigger', 'status_change')
                        ->where('action', 'api')
                        ->where('feedback_status_id', $enquiry->feedback_status)
                        ->orderBy('id', 'DESC')
                        ->first();
                    if ($automation_rule_api && $automation_rule_api->api != null) {
                        try {
                            $commonObj->postToWebHook($automation_rule_api->api, $post_data);
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                }
                /**------------------------AUTOMATION_END--------------------------------------**/

                if ($flag) {
                    return response()->json(['msg' => "Enquiry Updated.", 'status' => 'success']);
                } else {
                    return response()->json(['msg' => "Something went wrong, please try again later", 'status' => 'fail']);
                }
            } else {
                return response()->json(['msg' => $validator->messages(), 'status' => 'fail']);
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\BackendModel\Enquiry $enquiry
     * @return \Illuminate\Http\Response
     */
    public function destroy(Enquiry $enquiry, Request $request)
    {
        $id = $enquiry->pk_int_enquiry_id;
        if ($enquiry) {

            $flag = $enquiry->delete();
            if ($flag) {
                $enquiry
                    ->onlyTrashed()
                    ->find($id)
                    ->update(['deleted_by' => Auth::user()->pk_int_user_id]);
                EnquiryStatus::where('fk_int_enquiry_id', $id)->delete();

                $task = Task::where('enquiry_id', $id)->delete();

                $camp = CampaignLead::query()
                    ->where('lead_id', $id)
                    ->delete();


                try {
                    event(new CreateFollowup("Lead deleted", EnquiryFollowup::TYPE_DELETE, $id, Auth::user()->pk_int_user_id));
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }

                return response()->json(['msg' => "Enquiry Deleted.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Enquiry could not be deleted.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Enquiry not found.", 'status' => 'fail']);
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate($id)
    {
        $enquiry = Enquiry::where('pk_int_enquiry_id', $id)->first();
        if ($enquiry) {
            $enquiry->int_status = Variables::DEACTIVE;
            $flag = $enquiry->save();
            if ($flag) {
                return response()->json(['msg' => "Enquiry Deactivated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Enquiry could not be deactivated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Enquiry not found.", 'status' => 'fail']);
        }
    }

    /**
     *
     */
    public function activate($id)
    {
        $enquiry = Enquiry::where('pk_int_enquiry_id', $id)->first();
        if ($enquiry) {
            $enquiry->int_status = Variables::ACTIVE;
            $flag = $enquiry->save();
            if ($flag) {
                return response()->json(['msg' => "Enquiry Activated.", 'status' => 'success']);
            } else {
                return response()->json(['msg' => "Enquiry could not be activated.", 'status' => 'fail']);
            }
        } else {
            return response()->json(['msg' => "Enquiry not found.", 'status' => 'fail']);
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnquiry()
    {
        $data = DB::table('tbl_enquiry_purpose')
            ->join('tbl_enquiries', 'tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
            ->where('tbl_enquiry_purpose.fk_int_user_id', User::getVendorId())
            ->whereNull('tbl_enquiries.deleted_at')
            ->orderBy('pk_int_enquiry_id', 'DESC')
            ->get();

        if (sizeof($data) != 0) {
            return response()->json(['msg' => "Enquiry  detail Found.", 'status' => 'success', 'data' => $data]);
        } else {
            return response()->json(['msg' => "Enquiry purpose detail not found.", 'status' => 'fail']);
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function crmpage()
    {
        return view('backend.user-pages.crm.index');
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getWhatsappTemplate($id)
    {
        $userId = User::getVendorId();
        $roleId = Auth::user()->int_role_id;
        $template = WhatsappTemplate::where('fk_int_user_id', $userId)
            ->where('int_status', '1')
            ->get();
        $template1 = WhatsappTemplate::where('fk_int_user_id', $userId)
            ->where('int_status', '1')
            ->first();
        return View('backend.pages.enquiries.whatsapptemplate', compact('template', 'roleId', 'id', 'template1'));
    }

    /**
     * @param Request $request
     * @return mixed
     * @throws \Illuminate\Validation\ValidationException
     */
    public function sendWhatsapp(Request $request)
    {
        $input = $request->all();
        $this->validate($request, [
            'template' => 'required',
        ]);
        $id = $request->id;
        $template = $request->template;
        $enquiry = Enquiry::where('pk_int_enquiry_id', $id)->first();
        $templates = WhatsappTemplate::query()
            ->where('pk_int_whatsapp_template_id', $template)
            ->first();

        event(new CreateFollowup(note: $templates->text_whatsapp_template_description,
            log_type: EnquiryFollowup::WHATSAPP_HISTORY,
            enquiry_id: $id,
            created_by: Auth::user()->pk_int_user_id));

        return Redirect::away('https://wa.me/' . $enquiry->vchr_customer_mobile . '?text=' . urlencode(mb_convert_encoding($templates->text_whatsapp_template_description, 'UTF-8')) . '');
    }

    public function uploadEnquiriesStep1(Request $request)
    {
        $vendorId = User::getVendorId();
        Log::info('Uploading Enquiries Step 1', ['vendor_id' => $vendorId]);

        $data = [];
        $mimes = ['application/vnd.ms-excel', 'text/plain', 'text/csv', 'text/tsv'];
        if (!in_array($_FILES['contacts']['type'], $mimes)) {
            Flash::error('Validation Error : Unsupported  file');
            return back();
        }

        $import = new ExcelImport();
        Excel::import($import, $request->file('contacts'));
        $data = $import->jsonData;

        if ($import->count > 5000) {
            Flash::error('File is limited to containing a maximum of 5000 records in a single process.');
            return back();
        }

        $json_name = $vendorId . time() . '.json';

        $publicPutStatus = Storage::cloud()->put('uploads/import/' . $json_name, $data);
        Log::info('Uploading Enquiries Step 1 - file uploaded to server', ['vendor_id' => $vendorId, 'upload_status' => $publicPutStatus]);

        if (Auth::user()->pk_int_user_id == 196) {
            \Log::info('uploadEnquiriesStep1 - data ');
            \Log::info($data);
        }
        $jsonData = json_decode($data, true);

        if (Auth::user()->pk_int_user_id == 196) {
            \Log::info('jsonData');
            \Log::info($jsonData);
        }

        if (!$this->checkImportFileFields($jsonData[0])) {
            Flash::error('File is containing invalid header, Please verify the file.');
            return back();
        }

        $lead_import = new LeadImport();
        $lead_import->vendor_id = $vendorId;
        $lead_import->no_of_records = $import->count;
        $lead_import->no_of_duplicate_records = 0;
        $lead_import->no_of_imported_records = 0;
        $lead_import->file_name = $_FILES['contacts']['name'];
        $lead_import->json_file = $json_name;
        $lead_import->created_by = Auth::user()->pk_int_user_id;
        $lead_import->status = 0;
        $lead_import->campaign_id = request('campaign_id') ?? 0;
        if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
            $lead_import->staff_id = Auth::user()->pk_int_user_id;
        }
        $lead_import->save();

        $errors = [];
        $duplicates = 0;
        $invalid = 0;
        $fields = [];
        foreach ($jsonData as $i => $dat) {
            if (is_array($dat)) {
                if (isset($dat['mobile_number'])) {
                    $dat['mobile_number'] = str_replace(" ", "", $dat['mobile_number']);
                    $fields = [
                        'mobile_number' => 'required|numeric',
                        'country_code' => 'required|numeric',
                        'lead_source' => 'required',
                    ];
                }
                if ($this->clean($dat['alternate_numbers']) != "") {
                    $fields['alternate_numbers'] = 'numeric';
                }
                $validation = Validator::make((array)$dat, $fields, []);

                $dat['duplicate'] = '';
                if ($validation->fails()) {
                    $dat['duplicate'] = 'Invalid:';
                    if (is_array($validation->errors()->toArray())) {
                        foreach ($validation->errors()->toArray() as $key => $err) {
                            $dat['duplicate'] .=
                                '*' .
                                $err[0] .
                                '                                                                                                                                  ';
                        }
                    } else {
                        $dat['duplicate'] .= '* ' . $validation->errors()->first();
                    }
                    array_push($errors, ['phone_number' => $validation->errors()]);
                    $invalid++;
                }
            }
        }

        return view('backend.pages.enquiries.import', ['id' => $lead_import->id, 'duplicates' => $duplicates, 'errors' => $errors, 'invalid' => $invalid]);
    }

    public function uploadEnquiriesStep2(Request $request, $id)
    {
        $vendorId = User::getVendorId();
        $lead_import = LeadImport::where('id', $id)
            ->where('vendor_id', $vendorId)
            ->first();
        if (!$lead_import) {
            abort(404);
        }
        Log::info("Lead importation step 2 started", ['vendor_id' => $vendorId]);

        $jsondata = Storage::cloud()->url('uploads/import/' . $lead_import->json_file);

        Log::info("Lead importation step 2 started - uploaded file", ['vendor_id' => $vendorId, 'file_path' => $jsondata]);

        $client = new GuzzleHttp\Client(['verify' => false]);
        $response = $client->get($jsondata);
        $data = json_decode($response->getBody()->getContents());

        Log::info("Lead importation step 2 started - downloaded file", ['vendor_id' => $vendorId, 'data' => $data]);

        $errors = [];
        $fields = [];

        foreach ($data as $i => $dat) {
            if (is_object($dat)) {
                if (isset($dat->mobile_number)) {
                    $dat->mobile_number = str_replace(" ", "", $dat->mobile_number);
                    $fields = [
                        'mobile_number' => 'required|numeric',
                        'country_code' => 'required|numeric',
                        'lead_source' => 'required',
                    ];
                }
                if ($this->clean($dat->alternate_numbers) != "") {
                    $fields['alternate_numbers'] = 'numeric';
                }
                $validation = Validator::make((array)$dat, $fields, []);

                $dat->duplicate = '';
                if ($validation->fails()) {
                    $dat->duplicate = 'Invalid:';
                    if (is_array($validation->errors()->toArray())) {
                        foreach ($validation->errors()->toArray() as $key => $err) {
                            $dat->duplicate .=
                                '*' . $err[0] . '                                                                                                                                   ';
                        }
                    } else {
                        $dat->duplicate .= '* ' . $validation->errors()->first();
                    }
                    array_push($errors, ['phone_number' => $validation->errors()]);
                }
            }
        }
        if (count($errors) > 0) {
            LeadImport::query()
                ->where('id', $id)
                ->where('vendor_id', $vendorId)
                ->delete();
        }
        return [
            'data' => $data,
            'draw' => 0,
            'input' => ['_' => time()],
            'recordsFiltered' => sizeof($data),
            'recordsTotal' => sizeof($data),
        ];
    }

    public function uploadEnquiriesStep3(Request $request, $id)
    {
        $lead_import = LeadImport::query()
            ->where('id', $id)
            ->where('vendor_id', User::getVendorId())
            ->firstOrFail();

        LeadImportJob::dispatch($lead_import)->onQueue('lead-import');

        Flash::success('Lead import is currently in progress. It may take some time.');
        return redirect('/user/enquiries');
    }

    public function uploadEnquiriesList(Request $request)
    {
        $list = LeadImport::query()
            ->where('vendor_id', User::getVendorId())
            ->orderBy('created_at', 'DESC')
            ->get();

        return view('backend.pages.enquiries.import_list', ['list' => $list]);
    }

    public function exportHistory()
    {
        $list = ExportHistory::query()
            ->where('vendor_id', User::getVendorId())
            ->where('type', ExportHistory::LEAD)
            ->orderBy('created_at', 'DESC')
            ->get();

        return view('backend.pages.enquiries.export_history', ['list' => $list]);
    }

    public function DealExportHistory()
    {
        $list = ExportHistory::where('vendor_id', User::getVendorId())
            ->where('type', ExportHistory::DEAL)
            ->orderBy('created_at', 'DESC')
            ->get();

        return view('backend.pages.enquiries.export_history', ['list' => $list]);
    }

    /**
     * Enquiries excel import
     */
    public function uploadEnquiries(Request $request)
    {
        $vendorId = User::getVendorId();
        /*$request->validate([
                                                                                                                                                                'contacts' => 'required|mimes:xls,xlsx,csv',
                                                                                                                                                                ]);*/
        // $rule = [
        //     'contacts' => 'required',
        //     //'contacts'=>'mimes:csv'
        // ];
        $mimes = ['application/vnd.ms-excel', 'text/plain', 'text/csv', 'text/tsv'];
        //$message = ['photo.mimes' => 'Unsupported  file'];
        //$validator = validator::make($request->all(), $rule, $message);
        if (!in_array($_FILES['contacts']['type'], $mimes)) {
            Flash::error('Validation Error : Unsupported  file');
            return back();
        }
        $path = $request->file('contacts')->getRealPath();
        $data = Excel::load($path)->get();

        if ($data->count()) {
            $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
            foreach ($data as $key => $value) {
                $enquiry_type = EnquiryType::where('vchr_enquiry_type', isset($value->lead_source) ? $value->lead_source : $value->enquiry_type)
                    ->where(function ($where) use ($vendorId) {
                        $where->where('fk_int_user_id', $vendorId)->orWhere('fk_int_user_id', 0);
                    })
                    ->first();
                if ($enquiry_type) {
                    $fk_int_enquiry_type_id = $enquiry_type->pk_int_enquiry_type_id;
                } else {
                    $enquiry_type = new EnquiryType();
                    $enquiry_type->vchr_enquiry_type = isset($value->lead_source) ? $value->lead_source : $value->enquiry_type;
                    $enquiry_type->int_status = Variables::ACTIVE;
                    $enquiry_type->fk_int_user_id = $vendorId;
                    $enquiry_type->vendor_id = $vendorId;
                    $enquiry_type->save();
                    $fk_int_enquiry_type_id = $enquiry_type->pk_int_enquiry_type_id;
                }
                $fk_int_purpose_id = null;
                if (isset($value->purpose) && $value->purpose != '') {
                    $purpose = EnquiryPurpose::where('vchr_purpose', $value->purpose)
                        ->where('fk_int_user_id', $vendorId)
                        ->first();
                    if ($purpose) {
                        $fk_int_purpose_id = $purpose->pk_int_purpose_id;
                    } else {
                        $purpose = new EnquiryPurpose();
                        $purpose->vchr_purpose = $value->purpose;
                        $purpose->vchr_purpose_description = $value->purpose;
                        $purpose->fk_int_user_id = $vendorId;
                        $purpose->created_by = auth()->user()->pk_int_user_id;
                        $purpose->save();
                        $fk_int_purpose_id = $purpose->pk_int_purpose_id;
                    }
                }
                $fk_int_feedback_status_id = null;
                if ((isset($value->feedback_status) && $value->feedback_status != '') || (isset($value->status) && $value->status != '')) {
                    $feedback_status = FeedbackStatus::where('vchr_status', isset($value->status) ? $value->status : $value->feedback_status)
                        ->where('fk_int_user_id', $vendorId)
                        ->first();
                    if ($feedback_status) {
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    } else {
                        $feedback_status = new FeedbackStatus();
                        $feedback_status->vchr_status = isset($value->status) ? $value->status : $value->feedback_status;
                        $feedback_status->vchr_color = '#000000';
                        $feedback_status->fk_int_user_id = $vendorId;
                        $feedback_status->created_by = auth()->user()->pk_int_user_id;
                        $feedback_status->save();
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    }
                } else {
                    $feedback_status = FeedbackStatus::where('vchr_status', 'None')
                        ->where(function ($where) use ($vendorId) {
                            $where->where('fk_int_user_id', $vendorId);
                        })
                        ->first();
                    if ($feedback_status) {
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    } else {
                        $feedback_status = new FeedbackStatus();
                        $feedback_status->vchr_status = 'None';
                        $feedback_status->vchr_color = '#000000';
                        $feedback_status->fk_int_user_id = $vendorId;
                        $feedback_status->created_by = auth()->user()->pk_int_user_id;
                        $feedback_status->save();
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    }
                }
                $fk_int_lead_type_id = null;
                if (isset($value->lead_type) && $value->lead_type != '') {
                    $lead_types = LeadType::where('name', $value->lead_type)
                        ->where('vendor_id', $vendorId)
                        ->first();
                    if ($lead_types) {
                        $fk_int_lead_type_id = $lead_types->id;
                    } else {
                        $lead_types = new LeadType();
                        $lead_types->name = $value->lead_type;
                        $lead_types->vendor_id = $vendorId;
                        $lead_types->created_by = auth()->user()->pk_int_user_id;
                        $lead_types->save();
                        $fk_int_lead_type_id = $lead_types->id;
                    }
                }
                $district_id = null;
                if (isset($value->district) && $value->district != '') {
                    $district = District::where('name', $value->district)
                        ->where('vendor_id', $vendorId)
                        ->first();
                    if ($district) {
                        $district_id = $district->id;
                    } else {
                        $district = new District();
                        $district->name = $value->district;
                        $district->vendor_id = $vendorId;
                        $district->status = District::ACTIVE;
                        $district->save();
                        $district_id = $district->id;
                    }
                }
                $contact = Enquiry::where('mobile_no', $value->mobile_number)
                    // ->where('fk_int_enquiry_type_id', $fk_int_enquiry_type_id)
                    ->where('fk_int_user_id', $vendorId)
                    ->first();
                if (!$contact) {
                    $old_status_id = $contact->feedback_status;
                    $data1 = [
                        'vchr_customer_name' => $value->name,
                        'vchr_customer_mobile' => $value->mobile_number,
                        'mobile_no' => $value->mobile_number,
                        'fk_int_enquiry_type_id' => $fk_int_enquiry_type_id,
                        'fk_int_user_id' => $vendorId,
                        'vchr_customer_email' => $value->email_address,
                        'address' => $value->address,
                        'new_status' => Enquiry::SHOW,
                        'created_at' => isset($value->date) ? Carbon::parse(str_replace("/", "-", $value->date) . " 00:00:00") : Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'created_by' => Auth::user()->pk_int_user_id,
                        'vchr_customer_company_name' => $value->company_name,
                        'district_id' => $district_id,
                    ];
                    if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0) {
                        $data1['staff_id'] = Auth::user()->pk_int_user_id;
                        $data1['assigned_date'] = Carbon::now();
                    } else {
                        if (isset($value->staff_name)) {
                            $staffId = User::where('vchr_user_name', $value->staff_name)
                                ->where('parent_user_id', $vendorId)
                                ->first();
                            if ($staffId) {
                                $data1['staff_id'] = $staffId->pk_int_user_id;
                                $data1['assigned_date'] = Carbon::now();
                            }
                        }
                    }
                    if ($fk_int_feedback_status_id) {
                        $data1['feedback_status'] = $fk_int_feedback_status_id;
                    }
                    if ($fk_int_purpose_id) {
                        $data1['fk_int_purpose_id'] = $fk_int_purpose_id;
                    }
                    if (isset($value->country_code)) {
                        $data1['country_code'] = $value->country_code;
                        $data1['vchr_customer_mobile'] = $value->country_code . $value->mobile_number;
                    }
                    if (isset($value->feedback)) {
                        $data1['vchr_enquiry_feedback'] = $value->feedback;
                    }
                    if (isset($value->alternate_numbers)) {
                        $data1['more_phone_numbers'] = $value->alternate_numbers;
                    }
                    if (isset($value['vchr_customer_company_name'])) {
                        $data1['vchr_customer_company_name'] = $value->company_name;
                    }
                    //function_date	lead_type	purchase_date	weight
                    if (isset($value['function_date'])) {
                        $data1['function_date'] = $value->function_date;
                    }
                    if (isset($value['purchase_date'])) {
                        $data1['purchase_date'] = $value->purchase_date;
                    }
                    if (isset($value['weight'])) {
                        $data1['exp_wt_grams'] = $value->weight;
                    }
                    if ($fk_int_lead_type_id) {
                        $data1['lead_type_id'] = $fk_int_lead_type_id;
                    }
                    //
                    // $contact_array[] = $data;
                    $enquiry = Enquiry::create($data1);
                    try {
                        Enquiry::newLeadFunctions($enquiry->pk_int_enquiry_id);
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }

                    if ($enquiry && isset($value->notes)) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = $value->notes;
                        // $enquiryfollowups->log_type = EnquiryFollowup::TYPE_NOTE;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();
                        try {
                            event(new CreateFollowup($value->notes, EnquiryFollowup::TYPE_NOTE, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                    if ($enquiry && $fk_int_feedback_status_id) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = $fk_int_feedback_status_id;
                        // $enquiryfollowups->log_type = EnquiryFollowup::TYPE_STATUS;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();

                        try {
                            event(new CreateFollowup($fk_int_feedback_status_id, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id, $old_status_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                    if ($enquiry) {
                        if (isset($value['lead_type']) && $value->lead_type == 'Key Person') {
                            $lead_type = LeadType::where('vendor_id', $vendorId)
                                ->where('name', 'like', 'Key Person')
                                ->first();
                            if ($lead_type) {
                                $exist = EnquiryType::where('vendor_id', $vendorId)
                                    ->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)
                                    ->first();
                                if (!$exist) {
                                    $enquiry_source = new EnquiryType();
                                    $enquiry_source->vendor_id = $vendorId;
                                    $enquiry_source->vchr_enquiry_type = $enquiry->vchr_customer_name;
                                    $enquiry_source->fk_int_user_id = $vendorId;
                                    $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                                    $enquiry_source->int_status = Variables::ACTIVE;
                                    $enquiry_source->created_by = Auth::user()->pk_int_user_id;
                                    $enquiry_source->save();
                                }
                            }
                        }
                        if (isset($value['follow_up_date']) && $value['follow_up_date'] != '' && $value['follow_up_date'] != null) {
                            $follow = $this->addScheduleForNextFollowUp($enquiry->pk_int_enquiry_id, Carbon::parse($value->follow_up_date)->toDateString());
                        }
                        foreach ($additional_fields as $key => $additional_field) {
                            if (isset($value[strtolower(str_replace(' ', '_', $additional_field->field_name))]) && $additional_field->input_type != 6) {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                $additionalDetails->field_id = $additional_field->id;
                                $additionalDetails->field_name = $additional_field->field_name;
                                $additionalDetails->value = $value[strtolower(str_replace(' ', '_', $additional_field->field_name))];
                                $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                                $additionalDetails->save();
                            }
                        }
                    }
                }
            }
            // if (!empty($contact_array)) {
            //     Enquiry::insert($contact_array);
            // }
            Flash::success($data->count() . 'Insert Record Successfully');
            return back();
        } else {
            Flash::success($data->count() . ' Record found');
            return back();
        }
    }

    public function addLeadsToCampaign(Request $request)
    {
        $vendorId = User::getVendorId();
        $data = [];
        $mimes = ['application/vnd.ms-excel', 'text/plain', 'text/csv', 'text/tsv'];
        if (!in_array($_FILES['contacts']['type'], $mimes)) {
            Flash::error('Validation Error : Unsupported  file');
            return back();
        }
        $path = $request->file('contacts')->getRealPath();
        $import = new ExcelImport();
        Excel::import($import, $request->file('contacts'));
        $data = $import->jsonData;

        if ($import->count > 1000) {
            Flash::error('File is limited to containing a maximum of 1000 records in a single process.');
            return back();
        }

        $jsonData = json_decode($data);

        $leadIds = [];
        $enquir = Enquiry::query()->where('fk_int_user_id', $vendorId);
        $campaign = LeadCampaign::query()->findOrFail($request->integer('campaign_id'));
        foreach ($jsonData as $i => $dat) {
            $mobile = (string)(int)$dat->country_code . (string)(int)$dat->mobile_number;
            $obj = (clone $enquir)->where('vchr_customer_mobile', $mobile)
                ->where(function ($q) use ($dat, $campaign) {
                    if (!$dat->staff_name && $campaign->type != LeadCampaign::POOL) {
                        $q->where('staff_id', '!=', null);
                    } else {
                        $q->where('staff_id', '=', null);
                    }
                })
                ->where(function ($q) use ($campaign, $request, $vendorId) {
                    if ($campaign->type == LeadCampaign::POOL) {
                        $q->whereDoesntHave('campaignLead', function ($q) use ($request, $vendorId) {
                            $q->where('vendor_id', $vendorId);
                            $q->where('campaign_id', $request->campaign_id);
                        });
                    } else {
                        $q->whereDoesntHave('campaignLead', function ($q) use ($request, $vendorId) {
                            $q->where('vendor_id', $vendorId);
                            $q->where('campaign_id', $request->campaign_id);
                            $q->whereHas('tasks', function ($query) use ($request, $vendorId) {
                                $query->where('campaign_id', $request->campaign_id);
                                $query->where('vendor_id', $vendorId);
                                $query->where('status', 0);
                            });
                        });
                    }
                })->first();

            if ($obj && $dat->staff_name) {
                $user = User::where('vchr_user_name', $dat->staff_name)->first();
                if ($user) {
                    $obj->staff_id = $user->pk_int_user_id;
                    $obj->assigned_date = Carbon::today();
                    $obj->save();

                    try {
                        $note = $user->vchr_user_name . " has been designated as the lead via campaign lead upload";
                        event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $obj->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
                array_push($leadIds, $obj->pk_int_enquiry_id);
            } elseif ($obj) {
                array_push($leadIds, $obj->pk_int_enquiry_id);
            }
        }

        AttachLeadsToCampaign::dispatch($campaign, $leadIds, auth()->user()->pk_int_user_id);

        Flash::success('Lead Import in Progress');

        return back();
    }

    public function addScheduleForNextFollowUp(int $enquiry_id, string $follow_up_date, string $time = null)
    {
        // $date = Carbon::createFromFormat('Y-m-d', $follow_up_date);
        // $date = $date->addDays(1);
        $newDate = \Carbon\Carbon::createFromFormat('Y-m-d', $follow_up_date)->format('m/d/Y');
        if (Enquiry::where('pk_int_enquiry_id', $enquiry_id)->exists()) {
            if ($time != null) {
                DB::table('tbl_enquiries')
                    ->where('pk_int_enquiry_id', $enquiry_id)
                    ->update(['next_follow_up' => $newDate . ' ' . $time]);
            } else {
                DB::table('tbl_enquiries')
                    ->where('pk_int_enquiry_id', $enquiry_id)
                    ->update(['next_follow_up' => $newDate]);
            }

            $data = [];
            $data['name'] = 'Follow up';
            $data['duration'] = '';
            $data['note'] = 'Next follow up';
            $data['id'] = $enquiry_id;
            $data['date'] = $newDate;
            $data['followup_required'] = 1;
            $data['time'] = $time ? $time : null;

            EnquiryFollowup::addSchedule($data, User::getVendorId());

            return true;
        } else {
            return false;
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function export()
    {
        /**
         * @var User $user
         */
        $user = Auth::user();

        $vendorId = $user->getBusinessId();

        if (!$user->isOpsUser()) {
            abort('403', 'UnAuthorized Action');
        }

        $types = EnquiryType::query()
            ->where('int_status', '1')
            ->UsedOnly($vendorId)
            ->where('vendor_id', $vendorId)
            ->get();

        $purposes = EnquiryPurpose::query()->where('fk_int_user_id', $vendorId)->get();
        $enquiry_status = FeedbackStatus::query()->where('fk_int_user_id', $vendorId)->get();
        if ($user->isUser()) {
            $created = User::withTrashed()
                ->where('pk_int_user_id', Auth::user()->pk_int_user_id)
                ->first();
            $created_bys = User::StaffCheck()
                ->where('parent_user_id', Auth::user()->pk_int_user_id)
                ->get();
        } elseif ($user->isStaff()) {
            $created = User::withTrashed()
                ->where('pk_int_user_id', Auth::user()->pk_int_user_id)
                ->first();
            $created_bys = User::StaffCheck()
                ->where('parent_user_id', Auth::user()->parent_user_id)
                ->get();
        }
        $staffId = User::StaffCheck()
            ->whereNotIn('pk_int_user_id', ['3642', '4015'])
            ->where('int_role_id', User::STAFF)
            ->where('parent_user_id', $vendorId)
            ->orWhere('pk_int_user_id', $vendorId)
            ->get();
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }

        // for ivr lead report
        $telephony = CloudTelephonySetting::where('default', 1)
            ->where('vendor_id', $vendorId)
            ->first();
        if ($telephony) {
            if ($telephony->operator == 2) {
                $dids = CallMaster::where('vendor_id', $vendorId)
                    ->distinct()
                    ->where(function ($q) {
                        $q->whereNotNull('call_status')
                            ->whereNotNull('did')
                            ->whereNotIn('did', [2147483647]);
                    })
                    ->get(['did as did']);
            } else {
                $dids = Ivr::where('vendor_id', $vendorId)
                    ->distinct()
                    ->get(['called_number as did']);
            }
        } else {
            $dids = Ivr::where('vendor_id', $vendorId)
                ->distinct()
                ->get(['called_number as did']);
        }

        $date_by = collect();
        $date_by->push(['id' => 1, 'name' => 'Created']);
        $date_by->push(['id' => 2, 'name' => 'Updated']);

        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $date_by->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
        }
        $lead_campaigns = LeadCampaign::where('vendor_id', $vendorId)->select('id', 'name')->get();
        $branches = Branch::where('vendor_id', $vendorId)->where('status', 1)->get();
        return View('backend.pages.enquiries.export')
            ->with('purposes', $purposes)
            ->with('enquiry_status', $enquiry_status)
            ->with('created', $created)
            ->with('created_bys', $created_bys)
            ->with('staffId', $staffId)
            ->with('additional_fields', $additional_fields)
            ->with('types', $types)
            ->with('date_by', $date_by)
            ->with('dids', $dids)
            ->with('branches', $branches)
            ->with('lead_campaigns', $lead_campaigns);
    }

    public function reportDownload($filename)
    {
        $filePath = storage_path('app/exports/' . $filename);

        if (file_exists($filePath)) {
            return Response::download($filePath);
        }

        abort(404);
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */

    public function getProposalTemplate($id)
    {
        $userIdS = User::getVendorId();
        $roleId = Auth::user()->int_role_id;
        $template = Proposal::where('fk_int_user_id', $userIdS)
            ->where('int_status', '1')
            ->get();
        $userId = User::where('pk_int_user_id', $userIdS)
            ->where('int_status', '1')
            ->select('pk_int_user_id', 'vchr_user_name', 'email', 'mobile')
            ->first();
        $id = Enquiry::where('pk_int_enquiry_id', $id)
            ->where('int_status', '1')
            ->first();
        return View('backend.pages.enquiries.viewallproposal', compact('template', 'userId', 'id', 'template'));
    }

    public function getSmsTemplate($id)
    {
        $userId = User::getVendorId();
        $roleId = Auth::user()->int_role_id;
        $template = SmsTemplateCode::where('fk_int_user_id', $userId)
            ->where('int_status', '1')
            ->get();
        $template1 = SmsTemplateCode::where('fk_int_user_id', $userId)
            ->where('int_status', '1')
            ->first();
        return View('backend.pages.enquiries.smstemplate', compact('template', 'roleId', 'id', 'template1'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendSms(Request $request)
    {
        $input = $request->all();
        $userId = User::getVendorId();
        $enquiry = Enquiry::find($request->id);
        $defaultSenderId = SingleSMS::getSenderid($userId, EnquiryType::CRM);
        $defaultRoute = SingleSMS::getRoute($userId, EnquiryType::CRM);
        $apitemplate = CustomClass::userDefaultApiTemplate($userId);
        $message = $request->sms_template_code;
        if (!empty($apitemplate)) {
            $input['template_id'] = $apitemplate->pk_int_api_template_id;
            $input['template'] = $apitemplate->text_api_template_description;
            $response = CustomClass::urlReplacement($input['template'], $message, $enquiry->vchr_customer_mobile, $defaultSenderId);
            //History
            // $enquiryfollowups = new EnquiryFollowup();
            // $enquiryfollowups->note = $request->sms_template_code;
            // $enquiryfollowups->log_type = EnquiryFollowup::SMS_HISTORY;
            // $enquiryfollowups->enquiry_id = $request->id;
            // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
            // $enquiryfollowups->save();

            try {
                event(new CreateFollowup($request->sms_template_code, EnquiryFollowup::SMS_HISTORY, $request->id, Auth::user()->pk_int_user_id));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            Flash::Success('Message Send Successfully!');
            return redirect('/user/enquiry-sms' . '/' . $request->id)->with('Success', 'Message Send successfully!');
        } else {
            $send = new SingleSMS();
            $smsPanel = $send->getSmsPanel($defaultRoute, $userId);
            $balance = $send->getSMSBalance($userId, $defaultRoute, $smsPanel);
            if ($balance > 0) {
                if (strlen($message) != strlen(utf8_decode($message))) {
                    $messageType = 1;
                } else {
                    $messageType = 0;
                }
                if ($smsPanel->title == SmsPanel::ALERTBOX) {
                    $routeCode = $send->getRouteDetails($defaultRoute)->int_sms_route_code;
                    $smsUrl = $send->getSmsUrl($defaultSenderId, $enquiry->vchr_customer_mobile, $message, $defaultRoute, $routeCode, $userId, $messageType);
                    $smsCount = $send->getInputSMSCount($message, $messageType);
                    $templateId = $send->getSmsTemplateId($defaultRoute, $userId);
                    $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                    $insertSms = $send->storeSmsData($userId, $templateId, $enquiry->vchr_customer_mobile, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::CRM, $routeCode, $defaultRoute, '1', $smsCount);
                    $response1 = $send->sendSms($defaultSenderId, $enquiry->vchr_customer_mobile, $message, $routeCode, $balance, $templateId, $defaultRoute, $messageType, $userId, $smsUrl);

                    $response = $send->getResponse($insertSms, $response1, $templateId, $defaultRoute, $userId, $smsCount);
                } else {
                    $routeCode = $send->getRouteDetails($defaultRoute)->short_code;
                    $smsUrl = $send->getSmsMerabtUrl($defaultSenderId, $enquiry->vchr_customer_mobile, $message, $defaultRoute, $routeCode, $userId, $messageType);
                    $smsCount = $send->getInputSMSCount($message, $messageType);
                    $templateId = $send->getSmsTemplateId($defaultRoute, $userId);
                    $routeName = $send->getRouteDetails($defaultRoute)->vchr_sms_route;
                    $insertSms = $send->storeSmsData($userId, $templateId, $enquiry->vchr_customer_mobile, $defaultSenderId, $messageType, $routeName, $message, EnquiryType::CRM, $routeCode, $defaultRoute, '1', $smsCount);
                    $response = $send->sendSms($defaultSenderId, $enquiry->vchr_customer_mobile, $message, $routeCode, $balance, $templateId, $defaultRoute, $messageType, $userId, $smsUrl);
                    $response = $send->getMetabtResponse($insertSms, $response, $templateId, $defaultRoute, $userId, $smsCount);
                }
                //History
                $getDomainId = SmsApiCredentials::where('id', $templateId)->first()->sms_domain_id;
                $domain = SmsDomain::where('id', $getDomainId)->first()->domain;
                // $enquiryfollowups = new EnquiryFollowup();
                // $enquiryfollowups->note = $request->sms_template_code;
                // $enquiryfollowups->log_type = EnquiryFollowup::SMS_HISTORY;
                // $enquiryfollowups->enquiry_id = $request->id;
                // $enquiryfollowups->task_type = $templateId;
                // $enquiryfollowups->response = $response;
                // $enquiryfollowups->name = $domain;
                // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                // $enquiryfollowups->save();
                try {
                    $enquiryfollowups = event(
                        new CreateFollowup($request->sms_template_code, EnquiryFollowup::SMS_HISTORY, $request->id, Auth::user()->pk_int_user_id, null, $domain, null, null, null, null, $templateId, $response, null, null)
                    );
                    $enquiryfollowups = $enquiryfollowups[0];
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }

                $enquiryId = $send->updateEnquiry($enquiryfollowups->id, $insertSms);
                Flash::Success('Message Send Successfully!');
                return redirect('/user/enquiry-sms' . '/' . $request->id)->with('Success', 'Message Send successfully!');
            } else {
                Flash::Error('Insufficient Balance!');
                return redirect('/user/enquiry-sms' . '/' . $request->id)->with('Error', 'Insufficient Balance!');
            }
        }
    }

    public function assignAgent(Request $request)
    {
        $tasks_exist = Task::where('enquiry_id', $request->id)
            ->where('assigned_to', '!=', $request->staff_id)
            ->where('status', 0)
            ->count();

        if ($tasks_exist) {
            return response()->json(['msg' => "This lead contains already tasks assigned to different agents", 'status' => false]);
        }

        $enquiry = Enquiry::where('pk_int_enquiry_id', $request->id)->first();
        $old_Agent_id = $enquiry->staff_id;
        if ($request->staff_id == -1)
            $enquiry->staff_id = NULL;
        else {
            $enquiry->staff_id = $request->staff_id;
            $enquiry->assigned_date = Carbon::today();
        }

        $enquiry->save();

        if ($request->staff_id != -1) {
            $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($request->staff_id);
            $vendor = User::select('vchr_user_name', 'pk_int_user_id')->find(User::getVendorId());
            if ($staff) {
                $staff->notify(new AssignLeadToStaff($enquiry, auth()->user()->pk_int_user_id));
                $vendor->notify(new AssignLeadToStaff($enquiry, auth()->user()->pk_int_user_id));
                event(new LeadAssigned($enquiry->pk_int_enquiry_id, $request->staff_id));
                try {
                    $note = $staff->vchr_user_name . " has been designated as the lead";
                    event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }
            }
        } else {
            try {
                $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($old_Agent_id);
                $note = ($staff) ? $staff->vchr_user_name . " has been unassigned from the agent." : "Agent UnAssigned";
                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
        }

        //Start: Send Notification
        // $notification_title = 'You have been assigned a new lead';
        // $notification_description = 'Lead Details: ' . $enquiry->name_with_number;
        // $notification_data = [
        //     "click_action" => "FLUTTER_NOTIFICATION_CLICK",
        //     "sound" => "default",
        //     "page" => "enquiry_details",
        //     "id" => (string)$request->id
        // ];
        // $user = User::find($request->staff_id);
        // if ($user && $user->fcm_token) {
        //     dispatch(new SendNotification([$user->fcm_token], $notification_title, $notification_description, $notification_data))->onQueue('notification');
        //     // SendNotification::dispatch([$user->fcm_token],'New lead assigned to you','New Lead: '.$enquiry->vchr_customer_name,[]);
        // }
        //End: Send Notification
        return response()->json(['msg' => "Agent Assigned", 'status' => true]);
    }

    public function assignAgentMultipleEnqs(Request $request)
    {
        if ($request->lead_ids == "") {
            return response()->json(['msg' => "Please Choose Lead", 'status' => false]);
        }
        $enq_ids = explode(',', $request->lead_ids);
        // $tasks_exist= Task::whereIn('enquiry_id',$enq_ids)->where('assigned_to','!=',$request->staff_id)->where('status',0)->count();
        // dd($tasks_exist);
        // if($tasks_exist)
        //     return response()->json(['msg' => "Some leads contains already tasks assigned to different agents", 'status' => false]);

        $taskUsed = Enquiry::whereIn('pk_int_enquiry_id', $enq_ids)
            ->whereHas('task', function ($q) {
                $q->where('assigned_to', '!=', request()->staff_id)->where('status', 0);
            })
            ->count();

        foreach ($enq_ids as $lead_id) {
            $enquiry = Enquiry::where('pk_int_enquiry_id', $lead_id)
                ->whereDoesntHave('task', function ($q) {
                    $q->where('assigned_to', '!=', request()->staff_id)->where('status', 0);
                })
                ->first();

            if ($enquiry) {
                $old_Agent_id = $enquiry->staff_id;
                if ($request->staff_id == -1) {
                    $enquiry->staff_id = NULL;
                } else {
                    $enquiry->staff_id = $request->staff_id;
                    $enquiry->assigned_date = Carbon::today();
                }

                $enquiry->save();

                if ($request->staff_id != -1) {
                    $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($request->staff_id);
                    if ($staff) {
                        $staff->notify(new AssignLeadToStaff($enquiry, auth()->user()->pk_int_user_id));
                        event(new LeadAssigned($enquiry->pk_int_enquiry_id, $request->staff_id));
                        try {
                            $note = $staff->vchr_user_name . " has been designated as the lead";
                            event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                } else {
                    try {
                        $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($old_Agent_id);
                        $note = ($staff) ? $staff->vchr_user_name . " has been unassigned from the agent." : "Agent UnAssigned";
                        event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, auth()->user()->pk_int_user_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
            }
        }

        $lead = $taskUsed == 1 ? 'lead' : 'leads';

        if ($taskUsed == count($enq_ids)) {
            return response()->json(['msg' => $taskUsed . " " . $lead . " contains already tasks assigned to different agents", 'status' => false]);
        }

        if ($taskUsed > 0) {
            return response()->json(['msg' => $taskUsed . " " . $lead . " contains already tasks assigned to different agents", 'status' => true]);
        } else {
            return response()->json(['msg' => "Agent Assigned", 'status' => true]);
        }

    }

    public function multipleEnquiryStatusUpdate(Request $request)
    {
        if ($request->leadids == "") {
            return response()->json(['msg' => "Please Choose Lead", 'status' => false]);
        }
        try {
            $leadids = explode(',', $request->leadids);
            foreach ($leadids as $leadid) {
                $enquiry = Enquiry::where('pk_int_enquiry_id', $leadid)
                    ->where('fk_int_user_id', User::getVendorId())
                    ->first();
                if ($enquiry) {
                    $old_status_id = $enquiry->feedback_status;
                    $enquiry->feedback_status = $request->status_id;
                    $enquiry->save();
                    try {
                        event(new CreateFollowup($request->status_id, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id, $old_status_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }

                    Enquiry::statusChangeFunction($enquiry);
                }
            }
            return response()->json(['msg' => "Status Updated", 'status' => true]);
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }
    }

    public function multipleEnquiryBulkUpdate(Request $request)
    {
        if ($request->bulk_leadids == "") {
            return response()->json(['msg' => "Please Choose Lead", 'status' => false]);
        }
        try {
            DB::beginTransaction();
            $vendorId = User::getVendorId();
            $leadids = explode(',', $request->bulk_leadids);
            if ($request->bulk_type_id) {
                $lead_type = LeadType::where('id', $request->bulk_type_id)->first();
            }
            if ($request->bulk_source_id) {
                $source = EnquiryType::where('pk_int_enquiry_type_id', $request->bulk_source_id)->first();
            }
            foreach ($leadids as $leadid) {
                $enquiry = Enquiry::where('pk_int_enquiry_id', $leadid)
                    ->where('fk_int_user_id', $vendorId)
                    ->first();
                if ($enquiry) {
                    if ($request->bulk_status_id) {
                        $old_status_id = $enquiry->feedback_status;
                        $enquiry->feedback_status = $request->bulk_status_id;
                    }
                    if ($request->bulk_purpose_id) {
                        $enquiry->fk_int_purpose_id = $request->bulk_purpose_id;
                    }
                    if ($request->bulk_type_id) {
                        $enquiry->lead_type_id = $request->bulk_type_id;
                    }
                    if ($request->bulk_source_id) {
                        $old_source_id = $enquiry->fk_int_enquiry_type_id;
                        $enquiry->fk_int_enquiry_type_id = $request->bulk_source_id;
                    }
                    $enquiry->save();
                    if ($request->bulk_status_id) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = $request->bulk_status_id;
                        // $enquiryfollowups->log_type = EnquiryFollowup::TYPE_STATUS;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();

                        try {
                            event(new CreateFollowup($request->bulk_status_id, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id, $old_status_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }

                        $automation_rule_email = AutomationRule::where('vendor_id', $vendorId)
                            ->where('trigger', 'status_change')
                            ->where('action', 'email')
                            ->where('feedback_status_id', $request->bulk_status_id)
                            ->orderBy('id', 'DESC')
                            ->first();
                        if ($automation_rule_email) {
                            AutomationRule::sendMail($enquiry->pk_int_enquiry_id, $automation_rule_email);
                        }
                    }
                    if ($request->bulk_purpose_id) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = $request->bulk_purpose_id;
                        // $enquiryfollowups->log_type = EnquiryFollowup::ENQ_PURPOSE;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();
                        try {
                            event(new CreateFollowup($request->bulk_purpose_id, EnquiryFollowup::ENQ_PURPOSE, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                    if ($request->bulk_type_id) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = "Lead Type Changed to " . ($lead_type ? $lead_type->name : '#Deleted Lead Type');
                        // $enquiryfollowups->log_type = EnquiryFollowup::TYPE_ACTIVITY;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();
                        try {
                            event(new CreateFollowup("Lead Type Changed to " . ($lead_type ? $lead_type->name : '#Deleted Lead Type'), EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                    if ($request->bulk_source_id) {
                        // $enquiryfollowups = new EnquiryFollowup();
                        // $enquiryfollowups->note = "Source Changed to " . ($source ? $source->vchr_enquiry_type : '#Deleted Source');
                        // $enquiryfollowups->log_type = EnquiryFollowup::TYPE_ACTIVITY;
                        // $enquiryfollowups->enquiry_id = $enquiry->pk_int_enquiry_id;
                        // $enquiryfollowups->created_by = Auth::user()->pk_int_user_id;
                        // $enquiryfollowups->save();
                        try {
                            event(new CreateFollowup("Source Changed to " . ($source ? $source->vchr_enquiry_type : '#Deleted Source'), EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, Auth::user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }

                        if ($old_source_id != $enquiry->fk_int_enquiry_type_id) {
                            $assignSourceData = AutomationRule::where('trigger', 'source_change')
                                ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                                ->where('vendor_id', $enquiry->fk_int_user_id)
                                ->where('action', 'assign')
                                ->orderby('id', 'DESC')
                                ->first();
                            if ($assignSourceData) {
                                AutomationRule::autoassign($assignSourceData, $enquiry->pk_int_enquiry_id);
                            }
                            $automation_rule_task = AutomationRule::where('trigger', 'source_change')
                                ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                                ->where('vendor_id', $enquiry->fk_int_user_id)
                                ->where('action', 'task')
                                ->orderby('id', 'DESC')
                                ->first();
                            if ($automation_rule_task) {
                                if ($automation_rule_task->duration) {
                                    $scheduled_date = Carbon::now()->addMinutes($automation_rule_task->duration);
                                } else {
                                    $scheduled_date = Carbon::now();
                                }

                                $input = [
                                    'name' => $automation_rule_task->task_title,
                                    'description' => $automation_rule_task->task_description,
                                    'scheduled_date' => $scheduled_date ?? now(),
                                    'task_category_id' => $automation_rule_task->task_category_id,
                                    'assigned_to' => $automation_rule_task->task_assigned_to,
                                    'assigned_by' => $enquiry->fk_int_user_id,
                                    'vendor_id' => $enquiry->fk_int_user_id,
                                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                                    'status' => 0,
                                ];
                                Task::create($input);
                            }
                            $show = new Common();
                            $automation_rule_campaign = $show->getRule($vendorId, 'source_change', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

                            if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                                if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                                    try {
                                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                                        $show->addToCampaign($automation_rule_campaign, $enquiry, Auth::user()->pk_int_user_id, $vendorId, $cmp->type);

                                    } catch (\Exception $e) {
                                        \Log::info($e->getMessage());
                                    }
                                }
                            }

                        }
                    }

                    // Additional field bulk update
                    $add_field = request('add_fields');
                    if (isset($add_field)) {
                        foreach ($add_field as $key => $value) {
                            if ($value) {
                                $field_name = LeadAdditionalField::find($key);
                                $exists_field = LeadAdditionalDetails::where('field_id', $key)
                                    ->where('field_name', $field_name->field_name)
                                    ->where('enquiry_id', $enquiry->pk_int_enquiry_id)
                                    ->first();
                                if ($exists_field) {
                                    $exists_field->value = $value;
                                    $exists_field->save();
                                } else {
                                    $field = new LeadAdditionalDetails();
                                    $field->field_id = $key;
                                    $field->field_name = $field_name->field_name;
                                    $field->enquiry_id = $enquiry->pk_int_enquiry_id;
                                    $field->value = $value;
                                    $field->created_by = Auth::user()->pk_int_user_id;
                                    $field->save();
                                }
                            }
                        }
                    }
                }
            }
            DB::commit();
            return response()->json(['msg' => "Updated", 'status' => true]);
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
            DB::rollback();
            return response()->json(['msg' => "Failed", 'status' => false]);
        }
    }

    /**
     * Remove lead import.
     */
    public function delete($id)
    {
        $flag = LeadImport::where('id', $id)->delete();
        if ($flag) {
            return response()->json(['msg' => "Deleted.", 'status' => 'success']);
        } else {
            return response()->json(['msg' => "Not found.", 'status' => 'fail']);
        }
    }

    public function setCustomFields(Request $request)
    {
        if (auth()->user()->int_role_id == 2 && $request->apply_for_all == 1) {
            $users = User::where('parent_user_id', User::getVendorId())->get();
            foreach ($users as $user) {
                $user->enquiry_display_fields = $request->fields;
                $user->save();
            }
        }
        $user = User::find(auth()->user()->pk_int_user_id);
        $user->enquiry_display_fields = $request->fields;
        $user->save();
        return 1;
    }

    public function validateMobile(Request $request)
    {
        return Enquiry::query()
            ->where('fk_int_user_id', User::getVendorId())
            ->where('mobile_no', $request->get('mobile'))
            ->when($request->has('id'), function ($query, $id) {
                return $query->where('pk_int_enquiry_id', '!=', $id);
            })
            ->exists();
    }

    public function campaignTaskRemove(Request $request): JsonResponse
    {
        if (!$request->has('lead_ids')) {
            return response()->json(['msg' => "Please Choose Lead", 'status' => false]);
        }

        $campaignId = $request->integer('campaign_id');

        LeadCampaign::query()
            ->whereKey($campaignId)
            ->where('is_complete', 0)
            ->existsOr(fn() => throw new ModelNotFoundException('campaign not found'));

        $leadIds = explode(',', $request->get('lead_ids'));

        DB::transaction(function () use ($leadIds, $campaignId) {
            Task::query()
                ->where('campaign_id', $campaignId)
                ->whereIn('enquiry_id', $leadIds)
                ->delete();

            CampaignLead::query()
                ->where('campaign_id', $campaignId)
                ->whereIn('lead_id', $leadIds)
                ->delete();
        });
        Event::dispatch(new LeadRemovedFromCampaign(campaignId: $campaignId, leadIds: $leadIds));

        return response()->json(['msg' => "Leads Removed from Campaign Successfully", 'status' => 'success']);
    }

    /**
     * <AUTHOR>
     * Find duplicate leads
     */
    public function findDuplicateLeads()
    {
        $userId = User::getVendorId();
        $whatsapp_session = UserWhatsappSession::where([
            'fk_int_user_id' => $userId,
            'status' => 1,
        ])->first();
        $district = District::where('vendor_id', $userId)
            ->where('status', 1)
            ->get();
        $taluk = Taluk::where('vendor_id', $userId)
            ->where('status', 1)
            ->get();
        $types = EnquiryType::where('int_status', '1')
            ->UsedOnly($userId)
            ->get();
        $staffId = User::where('int_role_id', User::STAFF)->where('parent_user_id', $userId);
        $agents = $staffId->get();
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();
            $staffId = $staffId->where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            });
        }
        $staffId = $staffId->get();
        $countrys = Country::All();
        $ip = Common::getIp();
        $data = \Location::get($ip);
        if (!$data) {
            $countryCodeIp = "IN";
        } else {
            $countryCodeIp = $data->countryCode;
        }

        $countryNewCheck = Country::where('country_code', $countryCodeIp)->first();
        if ($countryNewCheck) {
            $countryCode = $countryNewCheck->code;
        } else {
            $countryCode = '';
        }
        $additional_fields = LeadAdditionalField::where('vendor_id', $userId)->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2) {
                $additional_field->values = json_decode($additional_field->values, true);
            }
        }

        $fields = EnquiryFieldCustomise::where('vendor_id', $userId)->first();
        if ($fields) {
            $active_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $active_fields = null;
            $required_fields = null;
        }
        $purposes = EnquiryPurpose::where('fk_int_user_id', $userId)->get();
        $enquiry_status = FeedbackStatus::UsedStatuses($userId)->get();
        $lead_campaigns = LeadCampaign::where('vendor_id', $userId)->get();
        $agencies = Agency::where('vendor_id', $userId)->select('id', 'name')->get();
        if (Auth::user()->int_role_id == User::USERS) {
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::where('parent_user_id', Auth::user()->pk_int_user_id)->get();
            return View(
                'backend.pages.enquiries.duplicate-leads' . (request('fc') ? '-fc' : ''),
                compact(
                    'active_fields',
                    'required_fields',
                    'types',
                    'whatsapp_session',
                    'purposes',
                    'enquiry_status',
                    'created',
                    'created_bys',
                    'staffId',
                    'countrys',
                    'countryCode',
                    'additional_fields',
                    'district',
                    'taluk',
                    'lead_campaigns',
                    'agents',
                    'agencies'
                )
            );
        } elseif (Auth::user()->int_role_id == User::STAFF) {
            //echo Auth::user()->parent_user_id; die();
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::where('parent_user_id', Auth::user()->parent_user_id)->get();
            return View(
                'backend.pages.enquiries.duplicate-leads' . (request('fc') ? '-fc' : ''),
                compact(
                    'active_fields',
                    'required_fields',
                    'types',
                    'whatsapp_session',
                    'purposes',
                    'enquiry_status',
                    'created',
                    'created_bys',
                    'staffId',
                    'countrys',
                    'countryCode',
                    'additional_fields',
                    'district',
                    'taluk',
                    'lead_campaigns',
                    'agents',
                    'agencies'
                )
            );
        } else {
            $leads = [];
            $created = User::where('pk_int_user_id', Auth::user()->pk_int_user_id)->first();
            $created_bys = User::where('parent_user_id', Auth::user()->parent_user_id)->get();
            return View(
                'backend.pages.enquiries.adminindex',
                compact('active_fields', 'required_fields', 'types', 'whatsapp_session', 'purposes', 'enquiry_status', 'created', 'created_bys', 'staffId', 'countrys', 'countryCode', 'additional_fields', 'district', 'leads')
            );
        }
    }

    /**
     * @param Request $request
     * @return mixed
     * <AUTHOR>
     */
    public function getDuplicateLeads(Request $request)
    {
        $columns = [];
        $draw = $request->draw;
        $start = $request->start; //Start is the offset
        $length = $request->length; //How many records to show
        $column = $request->order[0]['column']; //Column to orderBy
        $dir = $request->order[0]['dir']; //Direction of orderBy
        $searchValue = $request->search['value']; //Search value
        $vendorId = User::getVendorId();
        //Sets the current page
        Paginator::currentPageResolver(function () use ($start, $length) {
            return $start == 0 ? 1 : $start / $length + 1;
        });
        //DB::enableQueryLog();
        //get day,month,year from date
        if ($request->created_at_from != null || $request->created_at_to != null) {
            $created_at_from = $request->created_at_from;
            $explodeCreatedAtFrom = explode(' ', $created_at_from);
            $fromdate = $explodeCreatedAtFrom[0];
            $explodeFromDate = explode('-', $fromdate);
            $from_year = $explodeFromDate[0];
            $from_month = $explodeFromDate[1];
            $from_day = $explodeFromDate[2];

            $created_at_to = $request->created_at_to;
            $explodeCreatedAtTo = explode(' ', $created_at_to);
            $todate = $explodeCreatedAtTo[0];
            $explodeToDate = explode('-', $todate);
            $to_year = $explodeToDate[0];
            $to_month = $explodeToDate[1];
            $to_day = $explodeToDate[2];
        }

        $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
            ->pluck('staff_id')
            ->toArray();

        $serviceSubscription = UserServiceSubscription::where('vendor_id', $vendorId)
            ->where('service', Variables::SERVICE_CRM)
            ->first();
        if ($serviceSubscription) {
            $userCount = $serviceSubscription->user_count;
        } else {
            $userCount = null;
        }

        if (Auth::user()->int_role_id == Variables::USER || Auth::user()->int_role_id == Variables::STAFF) {
            $enquiries = Enquiry::with(['last_followup', 'last_task'])
                ->leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
                ->leftJoin('tbl_enquiry_purpose as pp', 'tbl_enquiries.fk_int_purpose_id', '=', 'pp.pk_int_purpose_id')
                ->leftJoin('tbl_enquiry_types as src', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'src.pk_int_enquiry_type_id')
                ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
                ->leftJoin(DB::raw('tbl_users as staff'), 'tbl_enquiries.staff_id', '=', 'staff.pk_int_user_id')
                ->leftJoin('lead_types', function ($join) use ($vendorId) {
                    $join->on('tbl_enquiries.lead_type_id', '=', 'lead_types.id')->on('lead_types.vendor_id', '=', DB::raw($vendorId));
                })
                ->select(
                    'vchr_customer_name',
                    'followup_required',
                    DB::raw('IFNULL(lead_types.name,"") as lead_type'),
                    'vchr_enquiry_feedback',
                    'vchr_customer_email',
                    'vchr_customer_mobile',
                    'vchr_customer_company_name',
                    'et.vchr_enquiry_type as enquiry_type',
                    'pk_int_enquiry_id',
                    'tbl_enquiries.int_status',
                    'fk_int_purpose_id',
                    'vchr_purpose',
                    'fk_int_enquiry_type_id',
                    'vchr_color',
                    'feedback_status',
                    'tbl_users.vchr_user_name',
                    'tbl_enquiries.created_at as created_at',
                    'tbl_enquiries.updated_at',
                    'vchr_status',
                    'read_status',
                    'vchr_purpose',
                    'staff_id',
                    'lead_type_id',
                    'tbl_enquiries.next_follow_up',
                    DB::raw('IFNULL(staff.vchr_user_name,"") as staff_name'),
                    DB::raw('IFNULL(src.vchr_enquiry_type,"") as source')
                )
                ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                ->where(function ($query) use ($assignedStafId) {
                    if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
                        $query
                            ->where('tbl_enquiries.staff_id', Auth::user()->pk_int_user_id)
                            ->orwhere('tbl_enquiries.created_by', Auth::user()->pk_int_user_id)
                            ->orwhereIn('tbl_enquiries.staff_id', $assignedStafId);
                    }
                });
        } else {
            $enquiries = Enquiry::with(['last_followup', 'last_task'])
                ->leftJoin('tbl_enquiry_types as et', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'et.pk_int_enquiry_type_id')
                ->leftJoin('tbl_enquiry_purpose as pp', 'tbl_enquiries.fk_int_purpose_id', '=', 'pp.pk_int_purpose_id')
                ->leftJoin('tbl_enquiry_types as src', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'src.pk_int_enquiry_type_id')
                ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
                ->leftJoin('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
                ->leftJoin(DB::raw('tbl_users as staff'), 'tbl_enquiries.staff_id', '=', 'staff.pk_int_user_id')
                ->leftJoin('lead_types', 'tbl_enquiries.lead_type_id', '=', 'lead_types.id')
                ->select(
                    'vchr_customer_name',
                    'followup_required',
                    DB::raw('IFNULL(lead_types.name,"") as lead_type'),
                    'vchr_enquiry_feedback',
                    'vchr_customer_email',
                    'vchr_customer_mobile',
                    'vchr_customer_company_name',
                    'et.vchr_enquiry_type as enquiry_type',
                    'pk_int_enquiry_id',
                    'tbl_enquiries.int_status',
                    'fk_int_purpose_id',
                    'vchr_purpose',
                    'vchr_color',
                    'feedback_status',
                    'tbl_users.vchr_user_name',
                    'tbl_enquiries.created_at as created_at',
                    'tbl_enquiries.updated_at',
                    'vchr_status',
                    'read_status',
                    'vchr_purpose',
                    'staff_id',
                    'lead_type_id',
                    'tbl_enquiries.next_follow_up',
                    DB::raw('IFNULL(staff.vchr_user_name,"") as staff_name'),
                    DB::raw('IFNULL(src.vchr_enquiry_type,"") as source')
                );
            //->where('tbl_enquiries.fk_int_user_id', $vendorId)
            // ->orderby('pk_int_enquiry_id', 'DESC');
            //->get();
        }
        if (Auth::user()->int_role_id == Variables::USER || Auth::user()->is_co_admin == 1) {
            $enquiries->where('tbl_enquiries.fk_int_user_id', $vendorId);
        } elseif (Auth::user()->int_role_id == Variables::STAFF) {
            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', Auth::user()->designation_id)
                    ->where('menus.url', 'user/enquiriess')
                    ->exists()
            ) {
                // return 1;

                $enquiries
                    ->where('tbl_enquiries.staff_id', Auth::user()->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', null)
                    ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                    ->get();
            }
            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', Auth::user()->designation_id)
                    ->where('menus.url', 'user/enquiries')
                    ->exists()
            ) {
                $prvi = Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', Auth::user()->designation_id)
                    ->where('menus.url', 'user/enquiries');
                if ($prvi === null) {
                    abort('403', 'Unauthorized Action');
                }

                $enquiries
                    ->where('tbl_enquiries.staff_id', Auth::user()->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', Auth::user()->pk_int_user_id)
                    ->get();
            }

            if (
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', Auth::user()->designation_id)
                    ->where('menus.url', 'user/enquiries')
                    ->exists() and
                Privilege::join('menus', 'privileges.Privilage', '=', 'menus.id')
                    ->where('staff_id', Auth::user()->designation_id)
                    ->where('menus.url', 'user/enquiriess')
                    ->exists()
            ) {
                // return 4;
                // \Log::info('Privilege3');

                $enquiries
                    ->where('tbl_enquiries.staff_id', Auth::user()->pk_int_user_id)
                    ->orWhere('tbl_enquiries.staff_id', null)
                    ->where('tbl_enquiries.fk_int_user_id', $vendorId)
                    ->get();
            }
        }

        // return 'ahgdcs';
        if ($request->has('enquiry_type') && $request->enquiry_type != "") {
            if ($request->enquiry_type == -1) {
                $enquiries->whereNull('tbl_enquiries.fk_int_enquiry_type_id');
            } else {
                $enquiries->where('tbl_enquiries.fk_int_enquiry_type_id', $request->enquiry_type);
            }
        }
        if ($request->has('enquiry_purpose') && $request->enquiry_purpose != "") {
            if ($request->enquiry_purpose == -1) {
                $enquiries->whereNull('tbl_enquiries.fk_int_purpose_id');
            } else {
                $enquiries->where('tbl_enquiries.fk_int_purpose_id', $request->enquiry_purpose);
            }
        }
        if ($request->has('staff_id') && $request->staff_id != "") {
            if ($request->staff_id == "unassigned") {
                $enquiries->where('tbl_enquiries.staff_id', null);
            } else {
                $enquiries->where('tbl_enquiries.staff_id', $request->staff_id);
            }
        }
        if ($request->has('enquiry_status') && $request->enquiry_status != "") {
            if ($request->enquiry_status == -1) {
                $enquiries->whereNull('tbl_feedback_status.vchr_status');
            } else {
                $enquiries->where('tbl_enquiries.feedback_status', $request->enquiry_status);
            }
        }
        if ($request->has('sortby') && $request->filled('sortby') && $request->sortby == 1) {
            $date_field = 'tbl_enquiries.created_at';
        } elseif ($request->has('sortby') && $request->filled('sortby') && $request->sortby == 2) {
            $date_field = 'tbl_enquiries.updated_at';
        } else {
            $date_field = 'tbl_enquiries.created_at';
        }
        if ($request->has('created_at_from') && $request->created_at_from != "") {
            $enquiries->whereDate($date_field, '>=', $request->created_at_from);
        }
        if ($request->has('created_at_to') && $request->created_at_to != "") {
            $enquiries->whereDate($date_field, '<=', $request->created_at_to);
        }

        if ($request->has('created_by') && $request->created_by != "") {
            $enquiries->where('tbl_enquiries.created_by', $request->created_by);
        }
        if ($request->has('lead_type_id') && $request->filled('lead_type_id')) {
            if ($request->lead_type_id == -1) {
                $enquiries->whereNull('tbl_enquiries.lead_type_id');
            } else {
                $enquiries->where('tbl_enquiries.lead_type_id', $request->lead_type_id);
            }
        }

        if ($request->has('district_id') && $request->filled('district_id')) {
            $enquiries->where('tbl_enquiries.district_id', $request->district_id);
        }
        if ($request->has('filter') && $request->filter != "") {
            if ($request->filter == 'latest') {
                $wStart = Carbon::today()
                    ->subDays(7)
                    ->toDateString();
                $wEnd = Carbon::today()->toDateString();
                $enquiries->whereBetween('tbl_enquiries.created_at', [$wStart, $wEnd]);
            } elseif ($request->filter == 'today') {
                $enquiries->whereDate('tbl_enquiries.created_at', Carbon::today()->toDateString());
            } elseif ($request->filter == 'current-month') {
                $month_start = substr(new Carbon('first day of this month'), 0, 10) . ' 00:00:00';
                $month_end = substr(new Carbon('last day of this month'), 0, 10) . ' 23:59:59';
                $enquiries->whereBetween('tbl_enquiries.created_at', [$month_start, $month_end]);
            }
        }

        if ($request->has('status_filter') && $request->filled('status_filter')) {
            $enquiries->where('feedback_status', $request->status_filter);
        }
        if ($request->has('additional_ids') && $request->has('additional_val')) {
            $add_ids = $request->additional_ids;
            $add_val = $request->additional_val;
            if (!empty($add_ids) && !empty($add_val)) {
                foreach ($add_ids as $inde => $add_id) {
                    $tbl_alias = 'tbl_additional_table_' . $inde;
                    $enquiries->leftjoin('enquiry_additional_details as ' . $tbl_alias, $tbl_alias . '.enquiry_id', 'tbl_enquiries.pk_int_enquiry_id')->where($tbl_alias . '.field_id', $add_id);
                    if (is_array($add_val[$inde])) {
                        $enquiries->where(function ($where) use ($add_val, $tbl_alias, $inde) {
                            foreach ($add_val[$inde] as $key => $v) {
                                if ($key == 0) {
                                    $where->where($tbl_alias . '.field_id', $add_id);
                                    $where->where($tbl_alias . '.value', 'Like', '%' . $v . '%');
                                } else {
                                    $where->orWhere($tbl_alias . '.field_id', $add_id);
                                    $where->orWhere($tbl_alias . '.value', 'Like', '%' . $v . '%');
                                }
                            }
                        });
                    } else {
                        $enquiries->where($tbl_alias . '.value', $add_val[$inde]);
                    }
                }
            }
        }

        $enquiries = $enquiries->where(function ($where) use ($searchValue) {
            $where->where('vchr_customer_name', 'like', '%' . $searchValue . '%')->orWhere('vchr_customer_mobile', 'like', '%' . $searchValue . '%');
        });
        if ($request->followup_added) {
            if ($request->followup_added == 1) {
                $enquiries = $enquiries->whereHas('last_task');
            }
            if ($request->followup_added == 2) {
                $enquiries = $enquiries->whereDoesntHave('last_task');
            }
        }
        if ($request->follow_up_leads) {
            $today_date = Carbon::now()->format('Y-m-d');
            $enquiries = $enquiries->whereHas('task', function ($q) use ($today_date) {
                $q->whereDate('scheduled_date', '<=', $today_date)->where('status', 0);
            });
        }
        if ($request->yet_to_contacts) {
            $enquiries->where(function ($where) {
                $where->whereNull('tbl_enquiries.staff_id')->whereNull('tbl_enquiries.feedback_status');
            });
        }
        // if ($request->campaign_id) {
        //     $campaign_leads = Task::where('campaign_id', $request->campaign_id)
        //         ->where(function ($where) use ($request) {
        //             if ($request->has('campaign_status') && $request->campaign_status == 1) {
        //                 $where->where('status', 1);
        //             } elseif ($request->has('campaign_status') && $request->campaign_status == -1) {
        //                 $where->where('status', 0);
        //             }
        //         })
        //         ->pluck('enquiry_id')
        //         ->toArray();
        //     // if($campaign_leads)
        //     $enquiries->whereIn('pk_int_enquiry_id', $campaign_leads);
        // }

        if ($request->unassigned != "true") {
            if ($request->campaign_id) {
                $enquiries->whereHas('campaignLead', function ($q) use ($request, $vendorId) {
                    $q->where('vendor_id', $vendorId);
                    $q->where('campaign_id', $request->campaign_id);
                    $q->whereHas('tasks', function ($query) use ($request, $vendorId) {
                        $query->where('campaign_id', $request->campaign_id);
                        $query->where('vendor_id', $vendorId);
                        $query->where(function ($q) use ($request) {
                            if ($request->has('campaign_status') && $request->campaign_status == 1) {
                                $q->where('status', 1);
                            } elseif ($request->has('campaign_status') && $request->campaign_status == -1) {
                                $q->where('status', 0);
                            }
                        });
                    });
                });
            }
        } else {
            $enquiries->where('tbl_enquiries.staff_id', null);
        }

        if ($request->has('sortby') && $request->filled('sortby') && $request->sortby == 1) {
            $enquiries->orderBy('created_at', 'DESC');
        } elseif ($request->has('sortby') && $request->filled('sortby') && $request->sortby == 2) {
            $enquiries->orderBy('updated_at', 'DESC');
        } else {
            $enquiries->orderBy('updated_at', 'DESC');
        }
        $enquiries = $enquiries->paginate($length);

        $i = 1;
        $additional_details = LeadAdditionalDetails::whereIn('enquiry_id', $enquiries->pluck('pk_int_enquiry_id')->toArray())->get();
        $additional_fields = LeadAdditionalField::where('vendor_id', $vendorId)->get();
        foreach ($enquiries as $key => $row) {
            $row_next_sc_date = $row->last_task; //Task::where('enquiry_id',$row->pk_int_enquiry_id)->select('scheduled_date')->orderBy('id','desc')->first();
            if ($row_next_sc_date) {
                $row->scheduled_date = $row_next_sc_date->scheduled_date;
                $row->task_scheduled_date = $row_next_sc_date->scheduled_date;
            }
            $row->slno = $i;
            $i++;
            //Datas
            $row->assign_agent =
                ' <div class="form-check form-check-assign" >
                                <input id="checkbox2" type="checkbox" data-enq_id="' .
                $row->pk_int_enquiry_id .
                '">
                                
                                </div>';

            if ($row->read_status == 0) {
                if ($row->vchr_customer_name != null) {
                    $row->cust_name =
                        '<span><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '"><!--<span class="d-tbl-noti">New</span>-->' .
                        '</strong><br>' .
                        $row->vchr_customer_name .
                        '</strong><br><div class="shortner">' .
                        '' .
                        $row->vchr_customer_company_name .
                        '</div></a></span>';
                } else {
                    $row->cust_name =
                        '<span><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '"><!--<span class="d-tbl-noti">New</span>-->' .
                        "No name" .
                        '</a></span>';
                }
            } else {
                if ($row->vchr_customer_name != null) {
                    $row->cust_name =
                        '<span><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '">' .
                        '<strong>' .
                        $row->vchr_customer_name .
                        '</strong><br><div class="shortner">' .
                        '' .
                        $row->vchr_customer_company_name .
                        '</div></a></span>';
                } else {
                    $row->cust_name =
                        '<span><a class="ks-izi-modal-trigger2" data-toggle="modal" data-target="#enquiry_detail" href="#" enquiry-id="' .
                        $row->pk_int_enquiry_id .
                        '">' .
                        'No name' .
                        '</a></span>';
                }
            }
            //Company Name
            $row->cust_company = '<span>' . ($row->vchr_customer_company_name ?? "No company name") . '</span>';
            //
            //Mobile Number
            $row->mobno = $row->vchr_customer_mobile ?? "No mobile no";

            //
            //Purpose
            $row->enq_type = '<span>' . ($row->fk_int_purpose_id ? $row->vchr_purpose : "None") . '</span>';
            //
            //Lead Type
            $row->lead_type = '<span>' . ($row->lead_type_id ? $row->lead_type : "") . '</span>';
            //
            //Status
            $row->feedback = '<span>' . ($row->vchr_status ?? "None") . '</span>';
            //Created By
            $row->created = '<span>' . ($row->vchr_user_name ?? "System") . '</span>';
            //
            try {
                if ($row->created_at) {
                    $diff = Carbon::now()->diffInDays(Carbon::parse($row->created_at));

                    $row->created_date =
                        '<span>' .
                        ($diff < 3 ? Carbon::parse($row->created_at)->diffForHumans() : Carbon::parse($row->created_at)->format('d M Y h:i A')) .
                        '</span>';
                } else {
                    $row->created_date = '<span>' . "None" . '</span>';
                }
                //
                if ($row->updated_at) {
                    $diff = Carbon::now()->diffInDays(Carbon::parse($row->updated_at));
                    $row->updated_date =
                        '<span>' .
                        ($diff < 3 ? Carbon::parse($row->updated_at)->format('d M Y h:i A') : Carbon::parse($row->updated_at)->format('d M Y h:i A')) .
                        '</span>';
                } else {
                    $row->updated_date = '<span>' . "None" . '</span>';
                }
            } catch (\Exception $exp) {
            }
            $row->feedback = '<span>' . ($row->vchr_status ?? "None") . '</span>';
            if ($row->int_status == 1) {
                $row->show =
                    '                     
                <a href="#"  class="btn btn-clean btn-hover-light-primary btn-sm btn-icon dot-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <img src="/backend/images/dots.png">
                </a>
                <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
                
                <ul class="navi navi-hover act">
                <li class="navi-item">
                <a href="#" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal">
                <span class="navi-icon mr-2">
                <img src="/backend/images/edit.png">
                </span>
                <span class="navi-text"> Edit</span>
                </a>
                </li>
                <li class="navi-item">
                <a href="#" id="delete_plan" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '"  class="navi-link py-2 enquiry-delete">
                <span class="navi-icon mr-2">
                <img src="/backend/images/delete.png">
                </span>
                <span class="navi-text"> Delete</span>
                </a>
                </li>
                <li class="navi-item">
                <a href="' .
                    'template-whatsapp/' .
                    $row->pk_int_enquiry_id .
                    '" target="_blank" class="navi-link py-2">
                <span class="navi-icon mr-2">
                <img src="/backend/images/whatsapp.svg" width="28px">
                </span>
                <span class="navi-text"> WhatsApp</span>
                </a>
                </li>
                <li class="navi-item">
                <a target="_blank" href="' .
                    'enquiry-sms/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                <span class="navi-icon mr-2">
                <img src="/backend/images/chat.svg" width="28px">
                </span>
                <span class="navi-text"> Message</span>
                </a>
                </li>
                <li class="navi-item">
                <a target="_blank" href="' .
                    'sales/orders/create/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
                <span class="navi-icon mr-2">
                <img src="/backend/images/cart.svg" width="28px">
                </span>
                <span class="navi-text"> Order</span>
                </a>
                </li>
                </ul>
                
                </div>
                ';
            } //If Status is not in active state
            else {
                $row->show =
                    '
                                                                                                                                                                                            
            <a href="#" class="btn btn-clean btn-hover-light-primary btn-sm btn-icon dot-icon" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <img src="assets/media/task/dots.png">
            </a>
            <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
            
            <ul class="navi navi-hover act">
            <li class="navi-item">
            <a href="#" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2 ks-izi-modal-trigger1" data-target="#ks-izi-modal-large1"  data-toggle="modal" >
            <span class="navi-icon mr-2">
            <img src="/backend/images/edit.png">
            </span>
            <span class="navi-text"> Edit</span>
            </a>
            </li>
            <li class="navi-item">
            <a href="#" id="delete_plan" enquiry-id="' .
                    $row->pk_int_enquiry_id .
                    '"  class="navi-link py-2 enquiry-delete">
            <span class="navi-icon mr-2">
            <img src="/backend/images/delete.png">
            </span>
            <span class="navi-text"> Delete</span>
            </a>
            </li>
            <li class="navi-item">
            <a href="' .
                    'template-whatsapp/' .
                    $row->pk_int_enquiry_id .
                    '" target="_blank" class="navi-link py-2">
            <span class="navi-icon mr-2">
            <img src="/backend/images/whatsapp.svg" width="28px">
            </span>
            <span class="navi-text"> WhatsApp</span>
            </a>
            </li>
            <li class="navi-item">
            <a target="_blank" href="' .
                    'enquiry-sms/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
            <span class="navi-icon mr-2">
            <img src="/backend/images/chat.svg" width="28px">
            </span>
            <span class="navi-text"> Message</span>
            </a>
            </li>
            <li class="navi-item">
            <a target="_blank" href="' .
                    'sales/orders/create/' .
                    $row->pk_int_enquiry_id .
                    '" class="navi-link py-2">
            <span class="navi-icon mr-2">
            <img src="/backend/images/cart.svg" width="28px">
            </span>
            <span class="navi-text"> Order</span>
            </a>
            </li>
            
            </ul>
            
            </div>
            ';
            }

            $assignedToUser = $row->staff_name; //User::where('pk_int_user_id', $enquiry->staff_id)->first();

            if ($assignedToUser == "") {
                $assignedTo = "Assign to Agent";
            } else {
                $assignedTo = $assignedToUser;
            }
            $row->assigned_to = '<span ><a href="javascript:showEditModal(' . $row->pk_int_enquiry_id . ')" >' . $assignedTo . '</a></span>';

            if ($row->task_scheduled_date) {
                $row->next_follow_up_data = '<span><a>' . Carbon::parse($row->task_scheduled_date)->diffForHumans() . '</a></span>';
            } else {
                $row->next_follow_up_data = '<span><a href="javascript:showNextFollowUpModal(' . $row->pk_int_enquiry_id . ')">Add follow up</a></span>';
            }

            $enquiry_additional_details = $additional_details->where('enquiry_id', $row->pk_int_enquiry_id);
            foreach ($additional_fields as $add_field) {
                if ($add_field->show_in_list == 1) {
                    $enquiry_additional_detail = $enquiry_additional_details->where('field_id', $add_field->id)->first();
                    if ($enquiry_additional_detail && $enquiry_additional_detail->value) {
                        $row['additional_' . $add_field->id] = $add_field->input_type == 8 ? implode(",", json_decode($enquiry_additional_detail->value)) : $enquiry_additional_detail->value;
                    } else {
                        $row['additional_' . $add_field->id] = '';
                    }
                }
            }
        }
        return ['draw' => $draw, 'recordsTotal' => $enquiries->total(), 'recordsFiltered' => $enquiries->total(), 'data' => $enquiries];
    }

    // To get the total followup count on the selected day
    public function followupCount()
    {
        try {
            $dateObject = Carbon::createFromFormat('Y-m-d', request('data'))->addDay(1)->format('Y-m-d');
            $count = Task::where('scheduled_date', '>=', request('data'))
                ->where('scheduled_date', '<', $dateObject)
                ->where('vendor_id', User::getVendorId())
                ->count();

            return response()->json(['msg' => "Status Updated", 'status' => 'success', 'data' => $count]);
        } catch (\Exception $e) {
            return response()->json(['msg' => "Server Error", 'status' => 'error', 'data' => 0]);
        }
    }

    public function searchAutocompleteEnquiry(Request $request)
    {
        if ($request->isNotFilled('term')) {
            return response()->json(['status' => 'Success', 'data' => []]);
        }

        /** @var User $user */
        $user = $request->user();
        $vendorId = $user->getBusinessId();

        $enquiry = Enquiry::query()
            ->where('fk_int_user_id', $vendorId)
            ->select('pk_int_enquiry_id as id', 'mobile_no', 'vchr_customer_name')
            ->when($this->isMeilieSearchEnabled($vendorId), function ($q) use ($vendorId) {
                $searchIds = Enquiry::search(request('term'))
                    ->where('fk_int_user_id', $vendorId)
                    ->query(fn($builder) => $builder->select('pk_int_enquiry_id'))
                    ->take(100)
                    ->get()
                    ->pluck('pk_int_enquiry_id');

                $q->whereIn('pk_int_enquiry_id', $searchIds);
            }, function ($q) {
                $searchedValue = '%' . request('term') . '%';
                $q->where(function ($where) use ($searchedValue) {
                    $where->where('mobile_no', 'LIKE', $searchedValue)
                        ->orWhere('vchr_customer_mobile', 'LIKE', $searchedValue)
                        ->orWhere('vchr_customer_name', 'LIKE', $searchedValue);
                });

            })
            ->limit(100)
            ->get();

        return response()->json(['status' => 'Success', 'data' => $enquiry]);
    }

    private function isMeilieSearchEnabled(int $vendorId): bool
    {
        return false;
        return FeatureReleaseChecker::isEnabled('meilisearch', new TargetContext($vendorId));
    }

    /**
     * Enquiries excel import
     */
    public function ecohealUploadEnquiries(Request $request)
    {

        $mimes = ['application/vnd.ms-excel', 'text/plain', 'text/csv', 'text/tsv'];

        if (!in_array($_FILES['contacts']['type'], $mimes)) {
            Flash::error('Validation Error : Unsupported  file');
            return back();
        }
        $path = $request->file('contacts')->getRealPath();
        $data = Excel::load($path)->get();
        // dd($data->first());

        foreach ($data as $key => $value) {
            // dd($value);
            if (strpos($value->mobile, "+")) {
                $num1 = str_replace('+', ' ', $value->mobile);
            } elseif (strpos($value->mobile, ",")) {
                $num1 = str_replace(',', ' ', $value->mobile);
            } elseif (strpos($value->mobile, "-")) {
                $num1 = str_replace('-', ' ', $value->mobile);
            } else {
                $num1 = $value->mobile;
            }

            $num = mb_split(" ", $num1, 2);
            $num = str_replace(' ', '', $num);

            if (count($num) > 1) {
                $enquiries = Enquiry::where('fk_int_user_id', User::getVendorId())
                    ->where('vchr_customer_mobile', $num[0] . $num[1])
                    ->first();
                if ($enquiries) {
                    // \Log::info($value->mobile);
                    // \Log::info($num);
                    // $row = new Enquiry;
                    // $row->vchr_customer_name = $value->enquirername;
                    // $row->vchr_customer_company_name = $value->company;

                    //     if (count($num) > 1) {
                    //         $row->country_code = $num[0];
                    //         $row->mobile_no = $num[1];
                    //     } else {
                    //         $row->mobile_no = null;
                    //     }

                    // $purpose_id = null;
                    // if ($value->source != null) {
                    //     $purposes = EnquiryType::where('vendor_id', User::getVendorId())->where('vchr_enquiry_type', $value->source)->first();
                    //     if ($purposes) {
                    //         $purpose_id = $purposes->pk_int_enquiry_type_id;
                    //     } else {
                    //         $purposes = new EnquiryType;
                    //         $purposes->pk_int_enquiry_type_id  = EnquiryType::max('pk_int_enquiry_type_id') + 1;
                    //         $purposes->vendor_id = User::getVendorId();
                    //         $purposes->vchr_enquiry_type = $value->source;
                    //         $purposes->int_status = 1;
                    //         $purposes->fk_int_user_id = User::getVendorId();
                    //         $purposes->created_by = NULL;
                    //         $purposes->fk_int_enquiry_id = NULL;
                    //         $purposes->save();
                    //         $purpose_id = $purposes->pk_int_enquiry_type_id;
                    //     }
                    // }
                    // $assignTo = null;
                    // if ($value->attendedby) {
                    //     $user = User::where('parent_user_id', User::getVendorId())->where('vchr_user_name', $value->attendedby)->where('vchr_user_mobile', $value->mobile)->first();
                    //     if ($user) {
                    //         $assignTo = $user->pk_int_enquiry_id;
                    //     }
                    // }
                    // $row->fk_int_enquiry_type_id = $purpose_id;
                    // $row->vchr_customer_mobile = $num[0] . $num[1];
                    // $row->fk_int_user_id = User::getVendorId();
                    // $row->address = $value->address;
                    // $row->save();
                    $enquiry = $enquiries->pk_int_enquiry_id;

                    /**
                     * add orders
                     */

                    if ($value->total) {
                        /**
                         * OrderItem
                         */
                        $order_item = [];
                        $excel_product = explode(',', $value->products);
                        // dd($excel_product);
                        $count = count($excel_product);
                        for ($i = 0; $i < $count - 1; $i++) {
                            $product_explode = explode(' ', $excel_product[$i]);
                            $product_data = count($product_explode);
                            $product_count = $product_explode[$product_data - 1];
                            array_pop($product_explode);
                            $prod = implode(' ', $product_explode);
                            $product = Product::where('fk_int_user_id', User::getVendorId())
                                ->where('name', $prod)
                                ->first(); //get product data
                            if ($product) {
                                // $order_item->id = OrderItem::max('id') + 1;
                                $item['product_name'] = $product->name;
                                $item['product_id'] = $product->id;
                                $item['quantity'] = $product_count;
                                array_push($order_item, $item);
                                // dd($order_item);
                            }
                        }
                        if (count($order_item) > 0) {
                            $order = new Order();
                            $order->id = Order::max('id') + 1;
                            $order->fk_int_user_id = User::getVendorId();
                            $order->fk_int_enquiry_id = $enquiry;
                            $order->staff_id = Auth::user()->pk_int_user_id;
                            $order->name = 'old Orders';
                            $order->total_amount = $value->total ?? 0;
                            $order->net_total = $value->total ?? 0;
                            $order->bill_no = $value->order_no;
                            $order->bill_date = Carbon::createFromFormat('m-d-Y', $value->invoice_date)->toDateString();
                            $order->payment_mode = $value->payment_mode;
                            $order->shipping_charge = $value->shipping_charge;
                            $order->status = 0;
                            $order->created_by = User::getVendorId();
                            $order->save();
                            // dd($order_item);
                            $employee_list = [];
                            foreach ($order_item as $info) {
                                $info['order_id'] = $order->id;
                                array_push($employee_list, $info);
                            }
                            OrderItem::insert($employee_list);
                            // dd($employee_list);
                        }

                        // dd($enquiries->where('pk_int_enquiry_id', $enquiry)->get(), $order->where('id', $order->id)->get(), $order_item->where('order_id', $order->id)->get() ?? null);
                    }
                }
            }
        }

        Flash::success($data->count() . 'Insert Record Successfully');
        return back();
    }

    public function downloadFile($json_file)
    {
        $jsondata = Storage::cloud()->url('uploads/import/' . $json_file);
        $check = Storage::cloud()->exists('uploads/import/' . $json_file);
        if ($check) {
            $file = LeadImport::where('json_file', $json_file)->first();
            $client = new GuzzleHttp\Client(['verify' => false]);
            $response = $client->get($jsondata);
            $post = json_decode($response->getBody()->getContents(), true);
            return Excel::download(new ImportExport($post), $file->file_name);

        } else {
            Flash::error('File missed or does not exists');
            return back();
        }
    }

    public static function clean($string)
    {
        $string = str_replace(' ', '', $string); // Replaces all spaces with hyphens.
        $string = str_replace('"', '', $string); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }


    public function importLeads()
    {
        return view('backend.pages.enquiries.import_leads');

    }

    public function importList()
    {
        return view('backend.pages.enquiries.import_list-v2');

    }

    public static function checkImportFileFields($da)
    {
        // Decode JSON string into an associative array
        $da = json_encode($da);
        $data = json_decode($da, true);
        if (array_key_exists('name', $data) && array_key_exists('country_code', $data) && array_key_exists('mobile_number', $data) && array_key_exists('lead_source', $data) && array_key_exists('email_address', $data) && array_key_exists('alternate_numbers', $data) && array_key_exists('company_name', $data))
            return true;
        else
            return false;
    }
}
