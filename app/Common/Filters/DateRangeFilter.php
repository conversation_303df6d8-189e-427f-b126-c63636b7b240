<?php

declare(strict_types=1);

namespace App\Common\Filters;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

final class DateRangeFilter
{
    public function __construct(
        public readonly ?string $filterBy = null,
        public readonly ?Carbon $startDate = null,
        public readonly ?Carbon $endDate = null,
        public readonly string $dateColumn = 'created_at'
    ) {
    }

    public function __invoke(Builder $builder): Builder
    {
        return $builder->when($this->filterBy, function (Builder $builder): void {
            $table = $builder->getModel()->getTable();
            $column = "{$table}.{$this->dateColumn}";

            match ($this->filterBy) {
                'today' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()]
                ),
                'yesterday' => $builder->whereBetween(
                    $column,
                    [Carbon::yesterday()->startOfDay(), Carbon::yesterday()->endOfDay()]
                ),
                'this_week' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->startOfWeek(), Carbon::today()->endOfWeek()]
                ),
                'last_7_days' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->subDays(7)->startOfDay(), Carbon::today()->endOfDay()]
                ),
                'last_week' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->subWeek()->startOfWeek(), Carbon::today()->subWeek()->endOfWeek()]
                ),
                'this_month' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->startOfMonth(), Carbon::today()->endOfMonth()]
                ),
                'last_month' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->subMonth()->startOfMonth(), Carbon::today()->subMonth()->endOfMonth()]
                ),
                'this_year' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->startOfYear(), Carbon::today()->endOfYear()]
                ),
                'last_30_days' => $builder->whereBetween(
                    $column,
                    [Carbon::today()->subDays(30), Carbon::today()]
                ),
                'custom' => $this->applyCustomDateRange($builder, $column),
                default => null
            };
        });
    }

    private function applyCustomDateRange(Builder $builder, string $column): void
    {
        if ($this->startDate instanceof Carbon && $this->endDate instanceof Carbon) {
            $builder->whereBetween($column, [
                Carbon::parse($this->startDate),
                Carbon::parse($this->endDate)
            ]);
        }
    }
}
