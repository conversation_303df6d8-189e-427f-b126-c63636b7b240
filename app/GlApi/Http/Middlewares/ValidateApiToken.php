<?php

declare(strict_types=1);

namespace App\GlApi\Http\Middlewares;

use App\BackendModel\GlApiTokens;
use App\GlApi\Http\Resources\ApiResponse;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ValidateApiToken
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $token = $request->input('token');

        if (empty($token)) {
            Log::warning('API request without token', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
            ]);

            return ApiResponse::error('API token is required', 401);
        }

        try {
            $apiToken = GlApiTokens::query()
                ->where('vchr_token', $token)
                ->first(['fk_int_user_id', 'vchr_token']);

            if (! $apiToken instanceof GlApiTokens) {
                Log::warning('Invalid API token attempt', [
                    'token' => substr($token, 0, 10) . '...', // Log partial token for security
                    'ip' => $request->ip(),
                ]);

                return ApiResponse::error('Invalid API token', 401);
            }

            $request->merge([
                'api_user_id' => $apiToken->fk_int_user_id,
            ]);

            $request->attributes->set('api_user_id', $apiToken->fk_int_user_id);

            Log::info('API token validated successfully', [
                'api_user_id' => $apiToken->fk_int_user_id,
            ]);

            return $next($request);

        } catch (Exception $e) {
            Log::error('Error validating API token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error('Authentication error', 500);
        }
    }
}
