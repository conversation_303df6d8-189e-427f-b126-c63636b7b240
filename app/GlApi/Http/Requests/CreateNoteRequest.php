<?php

declare(strict_types=1);

namespace App\GlApi\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Shared\Exceptions\PhoneNumberIsInvalid;
use Shared\ValueObjects\PhoneNumber;

final class CreateNoteRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'note' => ['required', 'string', 'max:2000'],
            'mobileno' => ['nullable', 'string', 'required_without:email'],
            'countrycode' => ['nullable', 'string', 'max:5'],
            'email' => ['nullable', 'email', 'max:255', 'required_without:mobileno'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'note.required' => 'Note content is required',
            'note.max' => 'Note cannot exceed 2000 characters',
            'mobileno.numeric' => 'Mobile number must be numeric',
            'mobileno.digits_between' => 'Mobile number must be between 8 and 14 digits',
            'mobileno.required_without' => 'Either mobile number or email is required',
            'countrycode.required_with' => 'Country code is required when mobile number is provided',
            'email.email' => 'Please provide a valid email address',
            'email.required_without' => 'Either email or mobile number is required',
        ];
    }

    public function parsePhoneNumber(): ?PhoneNumber
    {
        $number = $this->string('mobileno');

        if ($this->missing('mobileno') || $number->trim()->isEmpty()) {
            return null;
        }

        try {
            return new PhoneNumber($number->start('+')->value());
        } catch (PhoneNumberIsInvalid) {
            try {
                return new PhoneNumber($this->string('countrycode', '91')->append($number->ltrim('+'))->start(
                    '+'
                )->value());
            } catch (PhoneNumberIsInvalid) {
                throw PhoneNumberIsInvalid::forNumber($number->value());
            }
        }
    }

    /**
     * Prepare the data for validation.
     * Handle both mobileno and mobile_no field names by normalizing to mobileno.
     */
    protected function prepareForValidation(): void
    {
        // If mobile_no is provided but mobileno is not, copy mobile_no to mobileno
        if ($this->has('mobile_no') && ! $this->has('mobileno')) {
            $this->merge([
                'mobileno' => $this->get('mobile_no'),
            ]);
        }

        // Clean the mobile number by keeping only digits and leading + sign
        if (! $this->has('mobileno') || $this->get('mobileno') === null) {
            return;
        }

        $this->merge([
            'mobileno' => $this->string('mobileno')
                ->replaceMatches('/[^0-9]/', '')
                ->value(),
        ]);
    }
}
