<?php

declare(strict_types=1);

namespace App\GlApi\Http\Controllers;

use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use App\GlApi\Http\Requests\CreateNoteRequest;
use App\GlApi\Http\Resources\ApiResponse;
use App\GlApi\Http\Traits\HasGlApiAuthentication;
use App\Http\Controllers\Controller;
use App\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Shared\ValueObjects\PhoneNumber;

class CreateNoteController extends Controller
{
    use HasGlApiAuthentication;

    /**
     * Create a note for an existing enquiry
     */
    public function __invoke(CreateNoteRequest $request): JsonResponse
    {
        Log::info('Note creation api request received in GL Api', [
            'request' => $request->validated(),
        ]);

        try {
            // Get user ID from middleware
            $userId = $this->getApiUserId($request);
            $vendorId = User::getVendorIdApi($userId);

            $enquiry = $this->findEnquiry($request, $vendorId);

            if (! $enquiry instanceof Enquiry) {
                return ApiResponse::error('Enquiry not found', 404);
            }

            $this->createNote($request->string('note')->value(), $enquiry, $userId);

            Log::info('Note created successfully', [
                'phone_number' => (string) $request->parsePhoneNumber(),
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'vendor_id' => $vendorId,
                'user_id' => $userId,
            ]);

            return ApiResponse::success('Note added successfully');

        } catch (Exception $e) {
            Log::error('Note creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error($e->getMessage(), 500);
        }
    }

    /**
     * Find enquiry by mobile or email
     */
    private function findEnquiry(CreateNoteRequest $request, int $vendorId): ?Enquiry
    {
        $phoneNumber = $request->parsePhoneNumber();

        return Enquiry::query()
            ->where('fk_int_user_id', $vendorId)
            ->when($phoneNumber instanceof PhoneNumber, static function ($query) use ($phoneNumber): void {
                $query->where('vchr_customer_mobile', $phoneNumber->toPhoneNumber());
            }, static function ($query) use ($request): void {
                $query->where('vchr_customer_email', $request->string('email')->value());
            })
            ->first();
    }

    /**
     * Create note using event
     */
    private function createNote(string $note, Enquiry $enquiry, int $userId): void
    {
        event(new CreateFollowup(
            note: $note,
            log_type: EnquiryFollowup::TYPE_NOTE,
            enquiry_id: $enquiry->pk_int_enquiry_id,
            created_by: $userId
        ));
    }
}
