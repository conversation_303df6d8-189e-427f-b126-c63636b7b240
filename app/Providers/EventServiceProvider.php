<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     */
    protected $listen = [
        'App\Events\LeadAdded' => ['App\Listeners\LeadAddedAutomations'],
        'App\Events\LeadAssigned' => ['App\Listeners\SendLeadAssignedNotification'],
        'App\Events\TaskAssigned' => ['App\Listeners\SendTaskAssignedNotification'],
        'App\Events\TaskCompleted' => ['App\Listeners\SendTaskCompletedNotification'],
        'App\Events\LeadBulkAssigned' => ['App\Listeners\StoreLeadAssignedActivity'],
        'App\Events\ApiHistoryPost' => ['App\Listeners\ApiHistoryListener'],
        'App\Events\CreateFollowup' => ['App\Listeners\CreateFollowupListener'],
        'App\Events\DealTaskAssigned' => ['App\Listeners\SendDealTaskAssignedNotification'],
        'App\Events\DealTaskCompleted' => ['App\Listeners\SendDealTaskCompletedNotification'],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        parent::boot();

        //
    }
}
