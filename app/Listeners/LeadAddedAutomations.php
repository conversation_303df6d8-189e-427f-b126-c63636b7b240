<?php

declare(strict_types=1);

namespace App\Listeners;

use App\AutomationRule;
use App\BackendModel\WhatsappTemplate;
use App\Events\LeadAdded;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;

class LeadAddedAutomations
{
    /**
     * Handle the event.
     *
     * @param  =LeadAdded  $event
     */
    public function handle(LeadAdded $event): void
    {
        $lead = $event->lead;
        //Get Rules
        $rules = AutomationRule::query()
            ->where('vendor_id', $lead->fk_int_user_id)
            ->where('trigger', 'new_lead')
            ->orderBy('id', 'DESC')
            ->get();
        foreach ($rules as $rule) {
            if ($rule->enquiry_source_id && $rule->enquiry_source_id != $lead->fk_int_enquiry_type_id) {

            } else {
                switch ($rule->action) {
                    case 'whatsapp':
                        $this->sendWhatsApp($lead->fk_int_user_id, $lead, $rule);
                        break;
                    case 'api':
                        $this->callApi($lead->fk_int_user_id, $lead, $rule);
                        break;
                    default:
                        # code...
                        break;
                }
            }
        }

    }

    public function sendWhatsApp($vendorId, $lead, $rule): void
    {
        $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $rule->whatsapp_template_id)
            ->select('text_whatsapp_template_description')->first();
        if (! $whatsappTemplate) {
            return;
        }

        $gupshupObj = new GupShup();
        $credientails = WatsappCredential::where('vendor_id', $vendorId)
            ->where('status', 1)
            ->where('platform_id', 2)
            ->first();
        if (! $credientails) {
            return;
        }

        $data = [
            'api_key' => $credientails->access_key,
            'from_number' => $credientails->source_mobile_num,
            'app_name' => $credientails->template_name,
        ];
        $gupshupObj->sendWatsappMessageIndividal(
            $lead->country_code ?? '',
            $lead->vchr_customer_mobile,
            str_replace('{{name}}', $lead->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description),
            $data
        );
    }

    public function callApi($vendorId, $lead, $rule): void
    {

    }
}
