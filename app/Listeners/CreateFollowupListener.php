<?php

declare(strict_types=1);

namespace App\Listeners;

use App\BackendModel\EnquiryFollowup;
use App\Events\CreateFollowup;
use Exception;
use Illuminate\Support\Facades\Log;

class CreateFollowupListener
{
    /**
     * Handle the event.
     */
    public function handle(CreateFollowup $event): void
    {
        try {
            $enquiryfollowup = new EnquiryFollowup();
            $enquiryfollowup->note = $event->note;
            $enquiryfollowup->log_type = $event->log_type;
            $enquiryfollowup->enquiry_id = $event->enquiry_id;
            $enquiryfollowup->created_by = $event->created_by;
            $enquiryfollowup->duration = $event->duration;
            $enquiryfollowup->name = $event->name;
            $enquiryfollowup->date = $event->date;
            $event->updated_by ? $enquiryfollowup->updated_by = $event->updated_by : '';
            $event->task_id ? $enquiryfollowup->task_id = $event->task_id : '';
            $event->bcc ? $enquiryfollowup->bcc = $event->bcc : '';
            $event->task_type ? $enquiryfollowup->task_type = $event->task_type : '';
            $event->response ? $enquiryfollowup->response = $event->response : '';
            $event->assigned_to ? $enquiryfollowup->assigned_to = $event->assigned_to : '';
            $event->reminder ? $enquiryfollowup->reminder = $event->reminder : '';
            $event->old_status_id ? $enquiryfollowup->old_status_id = $event->old_status_id : '';
            $enquiryfollowup->save();
        } catch (Exception $e) {
            Log::info('Unable to save enquiry followup due to ' . $e->getMessage());
        }
    }
}
