<?php

declare(strict_types=1);

namespace App\Task\Http\Controllers\Web;

use App\Common\Filters\DateRangeFilter;
use App\Common\Scopes\ApplyFilters;
use App\Task;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class GetCallStatsController extends CacheableController
{
    protected function handle(Request $request): JsonResponse
    {
        /** @var User|null $user */
        $user = $request->user();

        if (! $user) {
            return response()->json([
                'msg' => 'Unauthorized user',
            ], 401);
        }

        $vendorId = $user->getBusinessId();
        $filterBy = $request->filled('filter_by') ? (string) $request->filter_by : null;
        $agentId = $request->filled('agent_id') ? (int) $request->agent_id : null;
        $branchId = $request->filled('branch_id') ? (int) $request->branch_id : null;

        $startDate = null;
        $endDate = null;

        if ($request->filled('date')) {
            $date = explode('-', $request->date);

            $startDate = Carbon::parse($date[0])->format('Y-m-d');
            $endDate = Carbon::parse($date[1])->format('Y-m-d');
        }

        $taskQuery = Task::nonCampaign()
            ->where('vendor_id', '=', $vendorId)
            ->when($branchId, static function (Builder $builder) use ($branchId): void {
                $builder->whereHas('agentData', static function (Builder $builder) use ($branchId): void {
                    $builder->where('branch_id', $branchId);
                });
            })
            ->when($user->isOpsStaff() && $agentId, static function (Builder $builder) use ($agentId, $branchId): void {
                $builder->where(static function (Builder $builder) use ($agentId, $branchId): void {
                    $builder->where('assigned_to', '=', $agentId)
                        ->orWhereIn('assigned_to', static function (QueryBuilder $builder) use (
                            $agentId,
                            $branchId
                        ): void {
                            $builder->select('staff_id')
                                ->from('agent_staffs')
                                ->where('agent_id', $agentId);

                            if (! $branchId) {
                                return;
                            }

                            $builder->join('tbl_users', 'tbl_users.pk_int_user_id', '=', 'agent_staffs.staff_id')
                                ->where('tbl_users.branch_id', $branchId);
                        });
                });
            })
            ->where('task_category_id', '=', 2)
            ->tap(new ApplyFilters([
                new DateRangeFilter($filterBy, $startDate, $endDate),
            ]));

        $totalCountQuery = (clone $taskQuery)
            ->selectRaw('COUNT(tasks.id) as call_count')
            ->selectRaw("'total_tasks' as call_type");

        $completedCountQuery = (clone $taskQuery)
            ->where('status', '=', 1)
            ->selectRaw('COUNT(tasks.id) as call_count')
            ->selectRaw("'completed_tasks' as call_type");

        $pendingCountQuery = (clone $taskQuery)
            ->where('status', '=', 0)
            ->where('scheduled_date', '>=', Carbon::today())
            ->selectRaw('COUNT(tasks.id) as call_count')
            ->selectRaw("'pending_tasks' as call_type");

        $overdueCountQuery = (clone $taskQuery)
            ->where('status', '=', 0)
            ->where('scheduled_date', '<', Carbon::today())
            ->selectRaw('COUNT(tasks.id) as call_count')
            ->selectRaw("'overdue_tasks' as call_type");

        $combinedQuery = $totalCountQuery
            ->unionAll($completedCountQuery)
            ->unionAll($pendingCountQuery)
            ->unionAll($overdueCountQuery);

        $results = $combinedQuery->get()
            ->keyBy('call_type')
            ->toArray();

        $totalTaskCount = $results['total_tasks']['call_count'] ?? 0;
        $completedTaskCount = $results['completed_tasks']['call_count'] ?? 0;
        $pendingTaskCount = $results['pending_tasks']['call_count'] ?? 0;
        $overdueTaskCount = $results['overdue_tasks']['call_count'] ?? 0;

        $data = [
            'total' => $totalTaskCount,
            'completed' => $completedTaskCount,
            'pending' => $pendingTaskCount,
            'overdue' => $overdueTaskCount,
        ];

        return response()->json([
            'msg' => 'success',
            'data' => [
                'data' => $data,
            ],
        ]);
    }
}
