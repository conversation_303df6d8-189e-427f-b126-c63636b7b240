<?php

declare(strict_types=1);

namespace App\Task\Http\Controllers\Web;

use App\BackendModel\Enquiry;
use App\BackendModel\FeedbackStatus;
use App\Common\Filters\DateRangeFilter;
use App\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class GetLeadStatsController extends CacheableController
{
    protected function handle(Request $request): JsonResponse
    {
        Log::info('Request received for lead stats', [
            'payload' => $request->all(),
        ]);

        /** @var User|null $user */
        $user = $request->user();

        if (!$user instanceof User) {
            return response()->json([
                'msg' => 'Unauthorized user',
            ], 401);
        }

        $branchId = $request->filled('branch_id') ? $request->integer('branch_id') : null;
        $filterBy = $request->filled('filter_by') ? (string)$request->get('filter_by') : null;

        $vendorId = $user->getBusinessId();
        $userId = $user->pk_int_user_id;

        $feedbackGrouped = Enquiry::query()
            ->selectRaw('feedback_status as status, COUNT(*) as count')
            ->where('fk_int_user_id', $vendorId)
            ->when($user->isOpsStaff(),
                static fn($query) => $query
                    ->where(static fn($query) => $query->whereIn('staff_id', static function ($subQuery) use (
                        $user
                    ): void {
                        $subQuery->select('staff_id')
                            ->from('agent_staffs')
                            ->where('agent_id', $user->pk_int_user_id);
                    })->orWhere('staff_id', $userId)))
            ->when($branchId, static function (Builder $builder) use ($branchId): void {
                $builder->whereIn('staff_id', static function ($subQuery) use ($branchId): void {
                    $subQuery->select('pk_int_user_id')
                        ->from('tbl_users')
                        ->where('branch_id', $branchId);
                });
            })
            ->when($filterBy, function (Builder $builder) use ($filterBy): void {
                $dateFilter = new DateRangeFilter(filterBy:$filterBy);
                $dateFilter($builder);
            })
            ->when($request->filled('start_date') && $request->filled('end_date'), function (Builder $builder) use ($request): void {
                $startDate = Carbon::parse($request->get('start_date'));
                $endDate = Carbon::parse($request->get('end_date'));
                $customDateFilter = new DateRangeFilter('custom', $startDate, $endDate, 'created_at');
                $customDateFilter($builder);
            })
            ->groupBy('feedback_status')
            ->pluck('count', 'status');

        $leadCountByFeedbackStatus = FeedbackStatus::query()
            ->select('pk_int_feedback_status_id as feedback_status', 'vchr_status', 'vchr_color')
            ->where(static function ($q) use ($vendorId): void {
                $q->where('tbl_feedback_status.fk_int_user_id', $vendorId)
                    ->orWhere('tbl_feedback_status.fk_int_user_id', 0);
            })
            ->whereIn('pk_int_feedback_status_id', $feedbackGrouped->keys())
            ->get()
            ->toBase()
            ->map(static function (FeedbackStatus $feedbackStatus) use ($feedbackGrouped) {
                $feedbackStatus->setAttribute(
                    'total',
                    $feedbackGrouped->get($feedbackStatus->getAttribute('feedback_status'))
                );
                return $feedbackStatus;
            });

        $records = Enquiry::query()
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->where('fk_int_user_id', $vendorId)
            ->when(
                $user->isOpsStaff(),
                static fn($query) => $query
                    ->where(static fn($query) => $query->whereIn('staff_id', static function ($subQuery) use (
                        $user
                    ): void {
                        $subQuery->select('staff_id')
                            ->from('agent_staffs')
                            ->where('agent_id', $user->pk_int_user_id);
                    })->orWhere('staff_id', $userId)))
            ->when($branchId, static function (Builder $builder) use ($branchId): void {
                $builder->whereIn('staff_id', static function ($subQuery) use ($branchId): void {
                    $subQuery->select('pk_int_user_id')
                        ->from('tbl_users')
                        ->where('branch_id', $branchId);
                });
            })
            ->when($filterBy, function (Builder $builder) use ($filterBy): void {
                $dateFilter = new DateRangeFilter($filterBy, null, null, 'created_at');
                $dateFilter($builder);
            })
            ->when($request->filled('start_date') && $request->filled('end_date'), function (Builder $builder) use ($request): void {
                $startDate = Carbon::parse($request->get('start_date'));
                $endDate = Carbon::parse($request->get('end_date'));
                $customDateFilter = new DateRangeFilter('custom', $startDate, $endDate, 'created_at');
                $customDateFilter($builder);
            })
            ->groupBy('date')
            ->get();

        if ($filterBy == 'this_year') {
            $groupedData = $records
                ->groupBy(static fn($item) => Carbon::parse($item['date'])->format('Y-m'))
                ->map(static fn($items) => [
                    'date' => $items[0]['date'],
                    'count' => $items->sum('count'),
                ])->values();

            $data['line_graph'] = $groupedData->pluck('count');
        } elseif ($filterBy == '') {
            $data['line_graph'] = [$records->sum('count')];
        } else {
            $data['line_graph'] = $records->pluck('count');
        }

        $data['feedback_status'] = $leadCountByFeedbackStatus;

        return response()->json([
            'msg' => 'success',
            'data' => [
                'data' => $data,
            ],
        ]);
    }
}
