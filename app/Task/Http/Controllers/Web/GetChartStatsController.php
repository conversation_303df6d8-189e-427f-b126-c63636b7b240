<?php

declare(strict_types=1);

namespace App\Task\Http\Controllers\Web;

use App\Deal;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\BackendModel\Enquiry;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Common\Scopes\ApplyFilters;
use App\Common\Filters\DateRangeFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;

final class GetChartStatsController extends CacheableController
{
    protected function handle(Request $request): JsonResponse
    {
        /** @var User|null $user */
        $user = $request->user();

        if (! $user) {
            return response()->json([
                'msg' => 'Unauthorized user',
            ], 401);
        }

        $branchId = $request->filled('branch_id') ? (int) $request->branch_id : null;
        $filterBy = $request->filled('filter_by') ? (string) $request->filter_by : null;

        $startDate = null;
        $endDate = null;

        if ($request->filled('date')) {
            $date = explode('-', $request->date);

            $startDate = Carbon::parse($date[0]);
            $endDate = Carbon::parse($date[1]);
        }

        $dealsQuery = $this->getDealsQuery(
            user: $user,
            branchId: $branchId,
            filterBy: $filterBy,
            startDate: $startDate,
            endDate: $endDate
        );

        $agentDealData = (clone $dealsQuery)
            ->leftJoin('tbl_users', 'pk_int_user_id', 'agent_id')
            ->where('tbl_users.int_status', '=', 1)
            ->groupBy('agent_id')
            ->select([
                'agent_id',
                DB::raw('SUM(deal_amount) AS total_sum'),
                DB::raw('count(*) as count'), 'vchr_user_name',
            ])
            ->get();

        $dealsPipelineData = (clone $dealsQuery)
            ->groupBy('deal_stage_id')
            ->select([
                'deal_stage_id',
                DB::raw('SUM(deal_amount) AS total_sum'),
                DB::raw('count(*) as count'),
                DB::raw(
                    'CASE WHEN deal_stage_name IS NULL THEN "No status Name" ELSE deal_stage_name END AS deal_stage_name'
                ),
            ])
            ->get();

        $enquiryQuery = $this->getEnquiryQuery(
            user: $user,
            branchId: $branchId,
            filterBy: $filterBy,
            startDate: $startDate,
            endDate: $endDate
        );

        $enquiryPurposeData = (clone $enquiryQuery)
            ->leftJoin('tbl_enquiry_purpose', 'pk_int_purpose_id', 'tbl_enquiries.fk_int_purpose_id')
            ->groupBy('tbl_enquiries.fk_int_purpose_id')
            ->select(['tbl_enquiries.fk_int_purpose_id', DB::raw('count(*) as count'), 'vchr_purpose'])
            ->get();

        $enquiryStatusData = (clone $enquiryQuery)
            ->leftJoin('tbl_enquiry_types', 'pk_int_enquiry_type_id', 'tbl_enquiries.fk_int_enquiry_type_id')
            ->whereNull('tbl_enquiry_types.deleted_at')
            ->groupBy('tbl_enquiries.fk_int_enquiry_type_id')
            ->select(['tbl_enquiries.fk_int_enquiry_type_id', DB::raw('count(*) as count'), 'vchr_enquiry_type'])
            ->get();

        $data['agent_wise_deals'] = $agentDealData;
        $data['deal_pipeline'] = $dealsPipelineData;

        $data['lead_purpose']['series'] = $enquiryPurposeData->pluck('count');
        $data['lead_purpose']['labels'] = array_map(
            static fn ($value) => $value ?? 'Null',
            $enquiryPurposeData->pluck('vchr_purpose')->toArray()
        );

        $data['lead_sorce']['series'] = $enquiryStatusData->pluck('count');
        $data['lead_sorce']['labels'] = $enquiryStatusData->pluck('vchr_enquiry_type');

        return response()->json([
            'msg' => 'success',
            'data' => [
                'data' =>$data,
            ]
        ]);
    }

    private function getDealsQuery(
        User $user,
        ?int $branchId = null,
        ?string $filterBy = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): Builder {
        $vendorId = $user->getBusinessId();
        $agentId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $isCoAdmin = $user->is_co_admin;

        $dealsQuery = Deal::query()
            ->where('vendor_id', '=', $vendorId)
            ->when($branchId, static function (Builder $builder) use ($branchId): void {
                $builder->whereHas('agent', static function (Builder $builder) use ($branchId): void {
                    $builder->where('branch_id', $branchId);
                });
            })
            ->when(($roleId === 3 && $isCoAdmin === 0), static function (Builder $builder) use (
                $agentId,
                $branchId
            ): void {
                $builder->where(static function (Builder $builder) use ($agentId, $branchId): void {
                    $builder->where('agent_id', '=', $agentId)
                        ->orWhereIn('agent_id', static function (QueryBuilder $builder) use (
                            $agentId,
                            $branchId
                        ): void {
                            $builder->select('staff_id')
                                ->from('agent_staffs')
                                ->where('agent_id', '=', $agentId);

                            if (! $branchId) {
                                return;
                            }

                            $builder->join('tbl_users', 'tbl_users.pk_int_user_id', '=', 'agent_staffs.staff_id')
                                ->where('tbl_users.branch_id', '=', $branchId);
                        });
                });
            })
            ->tap(new ApplyFilters([
                new DateRangeFilter($filterBy, $startDate, $endDate),
            ]));

        return $dealsQuery->leftJoin('deal_stages', 'pk_int_deal_stage_id', 'deal_stage_id')
            ->where('deal_stages.deal_category_id', '=', 3);
    }

    private function getEnquiryQuery(
        User $user,
        ?int $branchId = null,
        ?string $filterBy = null,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): Builder {
        $vendorId = $user->getBusinessId();
        $agentId = $user->pk_int_user_id;
        $roleId = $user->int_role_id;
        $isCoAdmin = $user->is_co_admin;

        return Enquiry::query()
            ->where('tbl_enquiries.fk_int_user_id', '=', $vendorId)
            ->when($branchId, static function (Builder $builder) use ($branchId): void {
                $builder->whereHas('assigned_user', static function (Builder $builder) use ($branchId): void {
                    $builder->where('branch_id', '=', $branchId);
                });
            })
            ->when(($roleId === 3 && $isCoAdmin === 0), static function (Builder $builder) use ($agentId, $branchId): void {
                $builder->where(static function (Builder $builder) use ($agentId, $branchId): void {
                    $builder->where('staff_id', '=', $agentId)
                        ->orWhereIn('staff_id', static function (QueryBuilder $builder) use (
                            $agentId,
                            $branchId
                        ): void {
                            $builder->select('staff_id')
                                ->from('agent_staffs')
                                ->where('agent_id', '=', $agentId);

                            if (! $branchId) {
                                return;
                            }

                            $builder->join('tbl_users', 'tbl_users.pk_int_user_id', '=', 'agent_staffs.staff_id')
                                ->where('tbl_users.branch_id', '=', $branchId);
                        });
                });
            })
            ->tap(new ApplyFilters([
                new DateRangeFilter($filterBy, $startDate, $endDate),
            ]));
    }
}
