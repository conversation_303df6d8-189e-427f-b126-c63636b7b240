<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreateFollowup
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $note;

    public $log_type;

    public $enquiry_id;

    public $created_by;

    public $duration;

    public $name;

    public $date;

    public $updated_by;

    public $task_id;

    public $bcc;

    public $task_type;

    public $response;

    public $assigned_to;

    public $reminder;

    public $data;

    public $old_status_id;

    /**
     * Create a new event instance.
     */
    public function __construct($note, $log_type, $enquiry_id, $created_by, $old_status_id = null, $duration = null, $name = null, $date = null, $updated_by = null, $task_id = null, $bcc = null, $task_type = null, $response = null, $assigned_to = null, $reminder = null)
    {
        $this->note = $note;
        $this->log_type = $log_type;
        $this->enquiry_id = $enquiry_id;
        $this->created_by = $created_by;
        $this->duration = $duration;
        $this->name = $name;
        $this->date = $date;
        $this->updated_by = $updated_by;
        $this->task_id = $task_id;
        $this->bcc = $bcc;
        $this->task_type = $task_type;
        $this->response = $response;
        $this->assigned_to = $assigned_to;
        $this->reminder = $reminder;
        $this->data = $reminder;
        $this->old_status_id = $old_status_id;

    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): Channel|array
    {
        return new PrivateChannel('channel-name');
    }
}
