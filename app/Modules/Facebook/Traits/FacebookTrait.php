<?php
namespace App\Modules\Facebook\Traits;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\WhatsappTemplate;
use App\Common\Common;
use App\Common\Notifications;
use App\Common\Variables;
use App\Events\ApiHistoryPost;
use App\Events\CreateFollowup;
use App\Events\SendPusherNotification;
use App\Facades\AutomationFacade;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Jobs\SendNotification;
use App\Modules\Facebook\Models\FacebookVendor;
use App\Modules\Facebook\Models\FbPage;
use App\Modules\Facebook\Models\FbPageAd;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\PusherSetting;
use App\User;
use Carbon\Carbon;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait FacebookTrait
{
    // private method for creating access token.
    private function saveVendorAccessToken($accessToken)
    {
        $longLivedToken = $accessToken;
        $expiresIn = 120 * 60 * 24 * 60; // 60 days in seconds
        $expiresAtTimestamp = now()->addSeconds($expiresIn);

        $id = $this->getFacebookVendor() ? ($this->getFacebookVendor()->count() + 1) : 1;

        Log::info('Facebook save vendor access token', [
            'id' => $id,
            'long_lived_token' => $longLivedToken,
            'expires_at_timestamp' => $expiresAtTimestamp,
        ]);

        FacebookVendor::create([
            'name' => 'Facebook New Connection #'.$id,
            'vendor_id' => $this->vendorId,
            'fb_access_token' => (string) $longLivedToken,
            'fb_token_expires_at' => $expiresAtTimestamp,
        ]);
    }

    private function getFacebookVendor()
    {
        // Implement your logic to get the vendor based on the current session or request
        return FacebookVendor::where('vendor_id',$this->vendorId)->first();
    }

    private function getFacebookConnections(){
        return FacebookVendor::where('vendor_id',$this->vendorId)->get();
    }

    private function deleteFacebookConnection($id){
        $connection = FacebookVendor::find($id);
        if($connection){
            $connection->delete();
            return true;
        }else
            return false;
    }

    private function updateFacebookConnection($id,$name){
        $connection = FacebookVendor::find($id);
        if($connection){
            $connection->name = $name;
            $connection->save();
            
            return true;
        }else
            return false;
    }

    // Get the vendor's page ID in your database
    private function getVendorPageId()
    {
        return $this->getFacebookVendor()?->fb_page_id ?? null;
    }

    // Get the vendor's access token
    private function getGlAccessToken()
    {
        
        if(!$this->getFacebookVendor()){
            return redirect('v1/facebook/');
        }
        
        // Check if the token is expired
        if (!Carbon::now()->greaterThan($this->getFacebookVendor()->fb_token_expires_at)) {
            // Refresh the token
            $oAuth2Client = $this->fb->getOAuth2Client();
            $accessToken = $oAuth2Client->getLongLivedAccessToken($this->getFacebookVendor()->fb_access_token);

            // Update the vendor's token
            $expiresAt = $accessToken->getExpiresAt() ? $accessToken->getExpiresAt()->getTimestamp() - time() : 60 * 60 * 24 * 60; // Default 60 days
            $this->getFacebookVendor()->updateAccessToken((string) $accessToken, $expiresAt);
        }

        return $this->getFacebookVendor()->fb_access_token;
    }

    // private method for creating access token.
    private function saveFacebookPage($data)
    {
        Log::info('Facebook save page', [
            'data' => $data,
        ]);

        $pageData = FbPage::where('vendor_id',$this->vendorId)->where('fb_page_id',$data['page_id'])->first();
        if($pageData){
            $pageData->page_name = $data['page_name'];
            $pageData->fb_vendor_id = $data['fb_connection_id'];
            $pageData->access_token = $data['page_access_token'];
            $pageData->save();

            return $pageData;
        }else{
            return FbPage::create([
                'vendor_id' => $this->vendorId,
                'page_name' => $data['page_name'],
                'fb_page_id' => $data['page_id'],
                'fb_vendor_id' => $data['fb_connection_id'],
                'access_token' => $data['page_access_token'],
            ]);
        }
    }

    // private method for creating access token.
    private function saveFacebookAd($data)
    {
        Log::info('Facebook save ad', [
            'data' => $data,
        ]);

        $adPage = FbPageAd::where('vendor_id',$this->vendorId)
            ->where('page_id', $data['page_id'])
            ->where('fb_page_ad_id', $data['adId'])
            ->first();

        if(!$adPage)
           $adPage = FbPageAd::create([
                'form_name' => $data['form_name'],
                'vendor_id' => $this->vendorId,
                'page_id' => $data['page_id'],
                'fb_page_ad_id' => $data['adId'],
                'access_token' => $data['page_access_token']
            ]);
        
        return $adPage;
    }

    private function checkWorkFlow($data){
        Log::info('Facebook check work flow');

        if(isset($data['work_flow_id']))
            return FbWorkFlow::find($this->decryptId($data['work_flow_id']));
           
            return FbWorkFlow::where('vendor_id',$data['vendor_id'] ?? $this->vendorId)
                        ->where('fb_page_id',$data['page_id'] ?? NULL)
                        ->where('fb_ad_id',$data['ad_id'] ?? NULL)
                        ->where('active',1)
                        ->first();
    }
    // private method for creating access token.
    public function saveWorkFlow($data)
    {
        Log::info('Facebook save work flow', [
            'data' => $data,
        ]);

        $workFlowData = $this->checkWorkFlow($data);
        if($workFlowData){
            $workFlowData->update([
                "workflow_name" => $data['workflow_name'],
                "fb_vendor_id" => $data['fb_connection_id'],
                "fb_page_id" => $data['page_id'],
                "fb_ad_id" => $data['adId'],
                "fb_adgroup_id" => $data['adgroup_id'],
                'mapped_keys' => $data['maped_keys'],
                "page_access_token" => $data['page_access_token'],
                "active" => $data['active'] ?? 1
            ]);
        }else{
            $workFlowData = FbWorkFlow::create([
                "vendor_id" =>  $this->vendorId,
                "workflow_name" => $data['workflow_name'],
                "fb_vendor_id" => $data['fb_connection_id'],
                "fb_page_id" => $data['page_id'],
                "fb_ad_id" => $data['adId'],
                "fb_adgroup_id" => $data['adgroup_id'],
                "mapped_keys" => $data['maped_keys'],
                "page_access_token" => $data['page_access_token'],
                "active" => $data['active'] ?? 1
            ]);
        }
        return $workFlowData;
    }

    private function fetchLeadDetails($fb,$facebookService,$leadgenId, $accessToken)
    {
        try {
            return $facebookService->fetchLeadDetails($fb,$leadgenId,$accessToken); 
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            Log::info('Graph returned an error: ' . $e->getMessage());
            return null;
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            Log::info('Facebook SDK returned an error: ' . $e->getMessage());
            return null;
        }
    }

    public static function checkMobile(Request $request){
        $removePlusSymbol = str_replace('+','',$request->mobileno);
       
        $numberCount = strlen($removePlusSymbol);
        
        // Read the JSON file
        $json = Storage::disk('public_storage')->get('json/data.json');
        // Decode the JSON data into a collection
        $countries = collect(json_decode($json, true));
        
        switch ($numberCount) {
            case 9:
                $request->merge([
                    'countrycode' => $request->countrycode ?? "",
                    'mobileno' => $removePlusSymbol
                ]);
            break;
            case 10:
                    $request->merge([
                        'countrycode' => $request->countrycode ?? "",
                        'mobileno' => $removePlusSymbol
                    ]);
                break;
            case 11:
                $getCodeIntl = self::splitString($removePlusSymbol,3);
                $c_codeIntl=$countries->firstWhere('phoneCode', $getCodeIntl[0]);
                if($c_codeIntl){
                    $request->merge([
                        'countrycode' =>$getCodeIntl[0],
                        'mobileno' => $getCodeIntl[1]
                    ]);
                }else{
                    $request->merge([
                        'countrycode' => $request->countrycode ?? "",
                        'mobileno' => $removePlusSymbol
                    ]);
                }
                break;
            case 12:
                    $getCode = self::splitString($removePlusSymbol,2);
                    $getCodeIntl = self::splitString($removePlusSymbol,3);
                    $c_code=$countries->firstWhere('phoneCode', $getCode[0]);
                    $c_codeIntl=$countries->firstWhere('phoneCode', $getCodeIntl[0]);
                    if($c_codeIntl){
                        $request->merge([
                            'countrycode' =>$getCodeIntl[0],
                            'mobileno' => $getCodeIntl[1]
                        ]);
                    }else if($c_code){
                        $request->merge([
                            'countrycode' =>$getCode[0],
                            'mobileno' => $getCode[1]
                        ]);
                    }else{
                        $request->merge([
                            'countrycode' =>$getCode[0],
                            'mobileno' => $getCode[1]
                        ]);
                    }
                break;
            default:
                    $getCode = self::splitString($removePlusSymbol,2);
                    $request->merge([
                        'countrycode' => $request->countrycode ?? "",
                        'mobileno' => $getCode[1]
                    ]);
                break;
        }

        return $request;
    }

    public static function splitString($string, $length) {
        if (strlen($string) >= $length) {
            $part1 = substr($string, 0, $length);
            $part2 = substr($string, $length);
            return [$part1, $part2];
        } else {
            return ["", $string]; // Return an empty part1 and the original string in part2
        }
    }

    public static function checkLead($request,$vendorId){
        return Enquiry::where('fk_int_user_id',$vendorId)
            ->where(function($q) use($request){
                $q->where('vchr_customer_mobile', $request->countrycode. $request->mobileno)   
                    ->orWhere('mobile_no', $request->mobileno);    
            })->first();
    }

    public static function validate($request){
        return validator()->make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'email',
            'mobileno' => 'nullable|string',
        ]);
    }

    public static function getLeaddata(array $leadData, string $parameter): array
    {
        if(isset($leadData)) {
            switch ($parameter) {
                case 'actual_data':
                    return $leadData[1] ?? [];
                    break;
                case 'sample_data':
                    return $leadData[0] ?? [];
                    break;

                default:
                    return [];
                    break;
            }
        }
        return [];
    }

    public function encryptId($id) {
        $cipher = 'aes-256-cbc';
        $encryptionKey = 'gl-facebook';
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($id, $cipher, $encryptionKey, 0, $iv);
        $encryptedData = base64_encode($encrypted . '::' . $iv);

        // Replace '/' with another character, e.g., '-'
        return str_replace('/', '-', $encryptedData);
    }

    public function decryptId($encryptedData) {
            $encryptionKey = 'gl-facebook';
            $cipher = 'aes-256-cbc';
        try {
            // Replace URL-safe characters with original ones
            $encryptedData = strtr($encryptedData, '-_', '+/');

            list($encrypted, $iv) = explode('::', base64_decode($encryptedData), 2);
            return openssl_decrypt($encrypted, $cipher, $encryptionKey, 0, $iv);
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return abort(404);
        }
    }

    public function insertLead($type, $mobileno, $vendor_id, $name, $email, $feedbacks, $countrycode, $companyName, $request){
        Log::info('Facebook Lead Insert Job', [
            'type' => $type,
            'mobileno' => $mobileno,
            'vendor_id' => $vendor_id,
            'name' => $name,
            'email' => $email,
            'feedbacks' => $feedbacks,
            'countrycode' => $countrycode,
            'companyName' => $companyName,
            'request' => $request->all(),
        ]);

        Log::info('-----hit save to facebook trait-----');
        Log::info("Facebook lead insert lead", [
            'request' => $request->all()
        ]);
        Log::info("Facebook Lead Insert Job", ['vendor_id' => $vendor_id]);
        $campaign_automation = false;
        $staff_id = $statusId = $purpose = $lead_type_id = null;
        $countrycode =  str_replace("+","",$countrycode);
        $type=strip_tags($type);
        Log::info('Facebook Lead Insert Job source- '. $type);

        // Check Lead exist or not
        $exist = self::checkLead($request,$vendor_id);

        $enquiryType = EnquiryType::where('vchr_enquiry_type', $type)->where(function ($where) use ($vendor_id) {
            $where->where('fk_int_user_id', $vendor_id);
            $where->orWhere('vendor_id', $vendor_id);
        })->first();
        if ($enquiryType) {
            $typeId = $enquiryType->pk_int_enquiry_type_id;
        }
        else {
            $enType = new EnquiryType();
            $enType->vchr_enquiry_type = $type;
            $enType->fk_int_user_id = $vendor_id;
            $enType->vendor_id = $vendor_id;
            $enType->int_status = "1";
            $fl = $enType->save();
            $typeId = $enType->pk_int_enquiry_type_id;
        }

        if (!$exist) {
            Log::info("Facebook Lead insert as a new lead", ['request' => $request->all()]);
            if ($request->has('status')) {
                $feedback_status = FeedbackStatus::where('vchr_status', $request->status)->where(function ($where) use ($vendor_id) {
                    $where->where('fk_int_user_id', $vendor_id);
                })->first();
                if (!$feedback_status) {
                    $feedback_status = new FeedbackStatus();
                    $feedback_status->vchr_status = $request->status;
                    $feedback_status->vchr_color = '#000000';
                    $feedback_status->fk_int_user_id = $vendor_id;
                    $feedback_status->created_by = $vendor_id;
                    $feedback_status->save(); 
                }
                $statusId = $feedback_status->pk_int_feedback_status_id;
            }

            if($request->has('purpose')){
                $feedback_purpose = EnquiryPurpose::where('vchr_purpose', $request->purpose)->where(function ($where) use ($vendor_id) {
                    $where->where('fk_int_user_id', $vendor_id);
                })->first();
                if(!$feedback_purpose){
                    $feedback_purpose = new EnquiryPurpose();
                    $feedback_purpose->vchr_purpose = $request->purpose;
                    $feedback_purpose->fk_int_user_id = $vendor_id;
                    $feedback_purpose->vchr_purpose_description = $request->purpose;
                    $feedback_purpose->created_by = $vendor_id;
                    $feedback_purpose->save();
                }
                $purpose = $feedback_purpose->pk_int_purpose_id;
            }

            if($request->has('type')){
                $leadType = LeadType::where('name', $request->type)->where(function ($where) use ($vendor_id) {
                    $where->where('vendor_id', $vendor_id);
                })->first();
                if(!$leadType){
                    $leadType = new LeadType();
                    $leadType->name = $request->type;
                    $leadType->vendor_id = $vendor_id;
                    $leadType->created_by = $vendor_id;
                    $leadType->save();
                }
                $lead_type_id = $leadType->id;
            }
        
            if ($request->staff_name) {
                $staff = User::where('vchr_user_name', $request->staff_name)->where('parent_user_id', $vendor_id)->first();
                if ($staff)
                    $staff_id = $staff->pk_int_user_id;
            }

            DB::beginTransaction();
            $feedback = new Enquiry();
            $feedback->vchr_customer_mobile = $countrycode . $mobileno;
            $feedback->mobile_no = $mobileno;
            $feedback->country_code = $countrycode;
            $feedback->read_status = 0;
            if ($staff_id)
                $feedback->staff_id = $staff_id;
            $feedback->fk_int_user_id = $vendor_id;
            $feedback->fk_int_enquiry_type_id = $typeId;
            $feedback->feedback_status = $statusId;
            $feedback->fk_int_purpose_id = $purpose;
            $feedback->lead_type_id = $lead_type_id;
            $feedback->vchr_customer_name = $name;
            $feedback->vchr_customer_email = $email;
            $feedback->vchr_enquiry_feedback = $feedbacks;
            $feedback->address = $request->address ?? '';
            $feedback->created_by = $vendor_id;
            $feedback->vchr_customer_company_name = $companyName;
            $flag = $feedback->save();

            DB::commit();

            try {
                event(new ApiHistoryPost(1,$feedback->pk_int_enquiry_id,0,$vendor_id,$typeId,2));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
            $show = new Common();
            $enquiry_id = $feedback->pk_int_enquiry_id;
            $addional_fields = LeadAdditionalField::where('vendor_id', $vendor_id)->get();
            foreach ($addional_fields as $add_field) {
                try {
                    $modifiedString = str_replace(" ", "_", $add_field->field_name);
                    if ($request->has($add_field->field_name) || $request->has($modifiedString)) {
                        if($add_field->input_type == 8){
                            LeadAdditionalDetails::updateOrCreate([
                                'enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id
                            ],
                            [
                                'field_name' => $add_field->field_name,
                                'value' => json_encode(explode(',',$request[$add_field->field_name] ?? $request[$modifiedString])),
                                'created_by' => $vendor_id
                            ]);
                        }
                        else {
                            LeadAdditionalDetails::updateOrCreate([
                                'enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id
                            ],
                            [
                                'field_name' => $add_field->field_name,
                                'value' => $request[$add_field->field_name] ?? $request[$modifiedString],
                                'created_by' => $vendor_id
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::info('Enquiry model additional field issue');

                    Log::info('Facebook Lead Insert Job', [
                        'type' => $type,
                        'mobileno' => $mobileno,
                        'vendor_id' => $vendor_id,
                        'name' => $name,
                        'email' => $email,
                        'feedbacks' => $feedbacks,
                        'countrycode' => $countrycode,
                        'companyName' => $companyName,
                        'request' => $request->all(),
                    ]);
                    Log::info('Facebook Lead exception', [
                        'request' => request()->all(),
                        'exception' => $e->getMessage(),
                    ]);
                }
            }

            /**--AUTOMATION_WHATSAPP---------------------------------------------**/
            $automation_whatsapp = AutomationRule::where('vendor_id', $feedback->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'whatsapp')
                ->where('enquiry_source_id', $feedback->fk_int_enquiry_type_id)
                ->orderBy('id', 'DESC')
                ->first();

            if ($automation_whatsapp) {
                $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                    ->select('text_whatsapp_template_description')->first();
                if ($whatsappTemplate) {
                    $gupshupObj = new GupShup();
                    $credientails = WatsappCredential::where('vendor_id', $feedback->fk_int_user_id)
                        ->where('status', 1)
                        ->where('platform_id', 2)
                        ->first();
                    if ($credientails) {
                        $data = [
                            "api_key" => $credientails->access_key,
                            "from_number" => $credientails->source_mobile_num,
                            "app_name" => $credientails->template_name
                        ];
                        $gupshupObj->sendWatsappMessageIndividal($feedback->country_id ?? '', $feedback->vchr_customer_mobile, str_replace("{{name}}", $feedback->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description), $data);
                    }
                }
            }
            /**--WHATSAPP---------------------------------------------**/

            /* ----------- Assign agent vise staff assign----- */
            if($request->has('department')){
                try{
                    AutomationRule::departmentViseAutoAssign($request,$feedback,$vendor_id);
                }catch (\Exception $e) {
                    Log::info($e->getMessage());
                }

                if($vendor_id == 2476)
                    AutomationRule::storeDepartmentToField($request,$feedback,$vendor_id);
            }
            /* ----------- Assign agent vise staff assign end ----- */

            /**--API---------------------------------------------**/
            $source = $feedback->fk_int_enquiry_type_id;
            $post_data = [
                'phone' => $feedback->vchr_customer_mobile,
            ];
            $automation_rule_api = $show->getRule($vendor_id, 'new_lead', 'api',$source);
            if ($automation_rule_api && $automation_rule_api->api != null) {
                try {
                    if($vendor_id == Variables::NIKSHAN_USER_ID){
                        // Nikshan automation
                        $usr = $feedback->assigned_user;
                        $extension = null;
                        if($usr){
                            $extension = $usr->userExtension ? $usr->userExtension->extension : null;
                        }
                        if($extension)
                            $show->postToIvrAutoCall($automation_rule_api->api,$extension, $post_data);
                    }
                } catch (\Exception $e) {
                    Log::info('newlead automation by api-'.$e->getMessage());
                }
            }
            /**--API---------------------------------------------**/

            // --------------------webhook--------------------------------

            $automation_rule_webhook = $show->getRule($vendor_id, 'new_lead', 'webhook');
            if (count($automation_rule_webhook) > 0) {
                $status = FeedbackStatus::where('pk_int_feedback_status_id', $feedback->feedback_status)->select('vchr_status')->first();
                $post_data = [
                    'customer_name' => $feedback->vchr_customer_name,
                    'email' => $feedback->vchr_customer_email,
                    'status' => ($status)? $status->vchr_status : 'New Status',
                    'phone' => $feedback->vchr_customer_mobile,
                    'mobile' => $feedback->mobile_no,
                    'flag' => "new_lead",
                ];
                foreach($automation_rule_webhook as $w_hook){
                    if ($w_hook->webhook_id != NULL) {
                        try{
                            $webHook = $show->getWebHookById($w_hook->webhook_id);
                        }catch(\Exception $e){
                            Log::info('getWebHookById-'.$e->getMessage());
                        }
                        if ($webHook) {
                            try{
                                $show->postToWebHook($webHook->url, $post_data);
                            }catch(\Exception $e){
                                Log::info('postToWebHook-'.$e->getMessage());
                            }
                        }
                    }
                }
            }
            
            try{
                AutomationFacade::newLeadFunctions($feedback->pk_int_enquiry_id);
            }catch (\Exception $e) {
                Log::info($e->getMessage());
            }

            //Start: Send Notification
            if ($staff_id) {
                $notification_title = 'You have been assigned a new lead';
                $notification_description = 'Lead Details: ' . $feedback->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$feedback->pk_int_enquiry_id
                ];

                /* ----------Notification---------- */
                    $result = Notifications::getUserTokens($staff_id);
                    if($result){
                        dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                    }
                    try{
                        $existPusher = PusherSetting::where('vendor_id',$vendor_id)->active()->first();
                        if($existPusher){
                            $message = $notification_title.' '.$notification_description;
                            event(new SendPusherNotification($staff_id, $existPusher,$message));
                            // dispatch(new SendPusherNotification($staff_id, $existPusher,$message))->onQueue('pusher-notification');
                        }
                    }catch(\Exception $e){
                        Log::info('Push notification error');
                    }
                /* ----------End Notification---------- */
            }

            
            /**--Assigned to Campaign--------------------------------------------- */
                try{
                    $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_campaign',$feedback->fk_int_enquiry_type_id);
                    if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != NULL) {
                        if($automation_rule_campaign->enquiry_source_id == $feedback->fk_int_enquiry_type_id){
                            $campaign_automation = true;
                            $cmp=LeadCampaign::find($automation_rule_campaign->campaign_id);
                            $show->addToCampaign($automation_rule_campaign,$feedback,$feedback->fk_int_user_id,$feedback->fk_int_user_id,$cmp->type);
                        }
                    }
                }catch(\Exception $e){
                    Log::info($e->getMessage());
                    Log::info('Automation failed campaign');
                }
            /**-- Assigned to Campaign ------------------------------------------- */
        }
        else{
            Log::info("Facebook Lead insert existing lead", ['request' => $request->all()]);
                try {
                    event(new ApiHistoryPost(1,$exist->pk_int_enquiry_id,1,$vendor_id,$typeId,2));
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }

                //Start: Send Notification
                $user_name = $exist->vchr_customer_name .'('.$exist->vchr_customer_mobile .')';
                $source = $exist->leadSource? 'via '.$exist->leadSource->vchr_enquiry_type :'';
                $notification_title = $user_name.' tried to contacted via '.($type ?? $source);
                $notification_description = 'Lead Details: ' . $exist->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$exist->pk_int_enquiry_id
                ];

                try{
                    /* ----------Notification---------- */
                    if($exist->staff_id){
                        $result = Notifications::getUserTokens($exist->staff_id);
                        if($result){
                            dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                        }
                    }
                    /* ----------End Notification---------- */
                }
                catch(\Exception $e){
                    Log::info('error from duplicate lead via pabbly');
                    Log::info('Facebook Lead Insert Job Duplicate', [
                        'type' => $type,
                        'mobileno' => $mobileno,
                        'vendor_id' => $vendor_id,
                        'name' => $name,
                        'email' => $email,
                        'feedbacks' => $feedbacks,
                        'countrycode' => $countrycode,
                        'companyName' => $companyName,
                        'request' => $request->all(),
                    ]);
                }

                //Update lead attension
                $exist->lead_attension = 1;
                $exist->updated_at = now();
                $exist->update();

                try { 
                    $source = ($type)? 'via '.$type : 'meta data';
                    $leadDetail = $exist->vchr_customer_name ? $exist->vchr_customer_name : $exist->vchr_customer_mobile;
                    $note =  $leadDetail. ' attempted to contact again '.$source;
                    event(new CreateFollowup($note, EnquiryFollowup::TYPE_NOTE, $exist->pk_int_enquiry_id, $vendor_id));
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }

            Log::info('Facebook Lead Insert Job Exists', [
                'type' => $type,
                'mobileno' => $mobileno,
                'vendor_id' => $vendor_id,
                'name' => $name,
                'email' => $email,
                'feedbacks' => $feedbacks,
                'countrycode' => $countrycode,
                'companyName' => $companyName,
                'request' => $request->all(),
            ]);
            return response()->json(['message'=>'Lead already exists', 'status' => 'fail']);
        }
        
        $enq = Enquiry::find($enquiry_id);
        if(!$enq->staff_id && !$campaign_automation){
            $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_data_pool', $enq->fk_int_enquiry_type_id);
            Log::info('check pool automation in facebook - ', ['automation data' => $automation_rule_campaign,'facebook lead '=>$enq]);
            if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                if ($automation_rule_campaign->enquiry_source_id == $enq->fk_int_enquiry_type_id) {
                    try {
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $enq, $feedback->fk_int_user_id, $feedback->fk_int_user_id, $cmp->type);

                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            }
            else
            {
            $enq->staff_id = $vendor_id;
            $enq->save();
            $note = "Admin has been designated as the lead via api.";
            event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enq->pk_int_enquiry_id,$vendor_id));
            }
        }

        Log::info('Facebook Lead Insert Job Saved', [
            'type' => $type,
            'mobileno' => $mobileno,
            'vendor_id' => $vendor_id,
            'name' => $name,
            'email' => $email,
            'feedbacks' => $feedbacks,
            'countrycode' => $countrycode,
            'companyName' => $companyName,
            'request' => $request->all(),
        ]);

        return $enquiry_id;
    }
}
