<?php

namespace Getlead\Sales\Controllers\API;

use Getlead\Sales\Helper;
use Illuminate\Http\Request;
use Getlead\Sales\Controllers\Controller;
use Validator;
use Auth;
use App\User;
use App\BackendModel\GlApiTokens;
use Illuminate\Support\Facades\DB;
use Getlead\Sales\Models\Product;
use Getlead\Sales\Models\Category;
use Getlead\Sales\Models\ProductCategory;
use Getlead\Sales\Models\ProductImage;
use Getlead\Sales\Resources\ProductResource;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use Getlead\Sales\Models\EnquirySalesField;
use Getlead\Sales\Models\Order;
use Getlead\Sales\Models\OrderItem;
use Getlead\Sales\Models\PaymentCollection;
use Getlead\Sales\Resources\OrderResource;
use Getlead\Sales\Resources\CategoryResource;
use Getlead\Woocommerce\Service\WooCommerceService;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;

class WooCommerceController extends Controller
{
    protected $commonObj, $imagePath;

    public function __construct(Request $request)
    {
        $this->commonObj = new Helper();
        $this->imagePath = '/uploads/ProductImage/';
    }

    public function productCreate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];
        if ($request->images) {
            foreach ($request->images as $v => $image) {
                $validate_fields = [
                    'images' . $v => ['mimes:jpg,png'],
                ];
            }
        }
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {

            $vendorId = null;
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
            }

            DB::beginTransaction();
            try {

                $slug = $this->commonObj->slugify($request->name);
                $product = new Product();
                $product->fk_int_user_id = $vendorId;
                $product->slug = $slug;
                $product->name = $request->name;
                $product->small_description = $request->short_description;
                $product->description = $request->description;
                $product->stock = ($request->stock_quantity) ?? 0;
                // $product->unit_id            =  $request->unit_id;
                $product->rate = $request->price;
                // $product->youtube_video_link =  $request->youtube_video_link;
                // $product->brand_id           =  $request->brand_id;
                $product->created_by = $check_token->fk_int_user_id;
                $product->woocommerce_id = $request->id;
                $product->woo_product = 1;
                if ($request->status == "publish") {
                    $product->status = 1;
                } else {
                    $product->status = 0;
                }
                $flag = $product->save();

                $v = 0;

                if (isset($request->images)) {
                    foreach ($request->images as $image) {
                        $pro_image = new ProductImage;
                        $pro_image->image = $image['src'];
                        $pro_image->product_id = $product->id;
                        $pro_image->save();
                    }
                }

                if (isset($request->categories)) {

                    foreach ($request->categories as $cate) {

                        $woocate = Category::where('woocommerce_id', $cate['id'])->first();
                        if ($woocate == null) {
                            $slug = $this->commonObj->slugify($cate['name']);
                            $category = new Category();
                            $category->fk_int_user_id = $vendorId;
                            $category->slug = $slug;
                            $category->name = $cate['name'];
                            $category->parent_id = 0;
                            $category->woocommerce_id = $cate['id'];
                            $category->woo_category = 1;
                            $category->save();

                        }

                        $pcate = new ProductCategory;
                        $pcate->product_id = $product->id;
                        $pcate->category_id = ($woocate) ? $woocate->id : $category->id;
                        $pcate->fk_int_user_id = $vendorId;
                        $pcate->created_by = $check_token->fk_int_user_id;
                        $pcate->save();
                    }


                }

                DB::commit();
                if ($flag)
                    return $this->response(200, false, 'Product Added', new ProductResource($product));
                else
                    return $this->response(200, true, 'Failed to add Product, Please try again');
            } catch (\Exception $e) {
                DB::rollback();
                // \Log::error('productadd'); 
                \Log::error($e->getMessage());
                return $this->response(201, true, '');
            }
        }
    }

    public function productUpdate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];
        if ($request->images) {
            foreach ($request->images as $v => $image) {
                $validate_fields = [
                    'images' . $v => ['mimes:jpg,png'],
                ];
            }
        }
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            $id = $request->id;
            $vendorId = null;
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
            }
            DB::beginTransaction();
            try {
                $images = array();

                $Product = Product::where('fk_int_user_id', $vendorId)->where('woocommerce_id', $id)->first();
                if ($Product) {
                    if ($request->name) {
                        $slug = $this->commonObj->slugify($request->name);
                        $Product->slug = $slug;
                        $Product->name = $request->name;
                    }
                    if ($request->short_description)
                        $Product->small_description = $request->short_description;
                    if ($request->description)
                        $Product->description = $request->description;
                    if ($request->stock)
                        $Product->stock = ($request->stock_quantity) ?? 0;
                    if ($request->price)
                        $Product->rate = $request->price;

                    $Product->created_by = $check_token->fk_int_user_id;
                    if ($request->status == "publish") {
                        $Product->status = 1;
                    } else {
                        $Product->status = 0;
                    }

                    $flag = $Product->save();

                    if (isset($request->images)) {
                        ProductImage::where('product_id', $Product->id)->delete();
                        foreach ($request->images as $v => $image) {
                            $pro_image = new ProductImage;
                            $pro_image->image = $image['src'];
                            $pro_image->product_id = $Product->id;
                            $pro_image->save();
                        }
                    }

                    if (isset($request->categories)) {
                        ProductCategory::where('product_id', $Product->id)->delete();
                        foreach ($request->categories as $cate) {
                            $woocate = Category::where('woocommerce_id', $cate['id'])->first();
                            if ($woocate == null) {
                                // $woo=new WooCommerceService;
                                // $res=$woo->getWooCategoryById($cate['id']);

                                $slug = $this->commonObj->slugify($cate['name']);
                                $category = new Category();
                                $category->fk_int_user_id = $vendorId;
                                $category->slug = $slug;
                                $category->name = $cate['name'];
                                $category->parent_id = 0;
                                $category->woocommerce_id = $cate['id'];
                                $category->save();

                            }

                            $pcate = new ProductCategory;
                            $pcate->product_id = $Product->id;
                            $pcate->category_id = ($woocate) ? $woocate->id : $category->id;
                            $pcate->fk_int_user_id = $vendorId;
                            $pcate->created_by = $check_token->fk_int_user_id;
                            $pcate->save();
                        }

                    }

                    DB::commit();

                    if ($flag)
                        return $this->response(200, false, 'Product Updated', new ProductResource($Product));
                    else
                        return $this->response(200, true, 'Failed to updated Product, Please try again');
                } else
                    return $this->response(200, true, 'Product does not exist');
            } catch (\Exception $e) {
                DB::rollback();
                \Log::error($e->getMessage());
                return $this->response(201, true, '');
            }

        }
    }


    public function productDelete(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];
        if ($request->images) {
            foreach ($request->images as $v => $image) {
                $validate_fields = [
                    'images' . $v => ['mimes:jpg,png'],
                ];
            }
        }
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
            }
            DB::beginTransaction();
            try {
                $id = $request->id;

                $Product = Product::where('fk_int_user_id', $vendorId)->where('woocommerce_id', $id)->first();

                if ($Product) {
                    ProductCategory::where('product_id', $Product->id)->delete();
                    ProductImage::where('product_id', $Product->id)->delete();
                    // $Product->categories()->detach();
                    // $Product->images()->detach();
                    $deleted = Product::where('fk_int_user_id', $vendorId)->where('id', $Product->id)->delete();
                } else
                    return $this->response(200, true, 'Product does not exist');

                DB::commit();
                if ($deleted) {
                    return $this->response(200, false, 'Product Deleted');
                } else {

                    return $this->response(200, true, 'Failed to delete Product');
                }

            } catch (\Exception $e) {
                DB::rollback();

                \Log::error($e->getMessage());
                return $this->response(201, true, 'Failed to delete Product, Please try again');
            }
        }
    }

    public function orderCreate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];

        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $vendorId = null;
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Inalid Token');
                }

                DB::beginTransaction();
                try {
                    $total_amount = 0;
                    $old_order = Order::where('woocommerce_id', $request->id)->first();
                    if (!$old_order) {
                        $enquiry = Enquiry::where('vchr_customer_mobile', str_replace("+", "", $request->billing['phone']))
                            ->where('fk_int_user_id', $check_token->fk_int_user_id)->first();
                        if (!$enquiry) {
                            if (strlen(str_replace("+", "", $request->billing['phone'])) == 12) {
                                $countryCode = substr(str_replace("+", "", $request->billing['phone']), 0, 2);
                                $phoneNumber = substr(str_replace("+", "", $request->billing['phone']), 2);
                            } else {
                                $countryCode = '91';
                                $phoneNumber = str_replace("+", "", $request->billing['phone']);
                            }
                            $enquiry = new Enquiry();
                            $enquiry->fk_int_user_id = $vendorId;
                            $enquiry->vchr_customer_name = $request->billing['first_name'] . ' ' . $request->billing['last_name'];
                            $enquiry->vchr_customer_company_name = $request->billing['company'];
                            $enquiry->vchr_customer_mobile = str_replace("+", "", $request->billing['phone']);
                            $enquiry->vchr_customer_email = $request->billing['email'];
                            $enquiry->mobile_no = $phoneNumber;
                            $enquiry->country_code = $countryCode;
                            $enquiry->created_by = $check_token->fk_int_user_id;
                            $enquiry->read_status = 0;
                            $enquiry->woocommerce_id = $request->customer_id;
                            $feedback_status = FeedbackStatus::where('vchr_status', 'New')->where(function ($where) use ($vendorId) {
                                $where->where('fk_int_user_id', $vendorId);
                            })->first();

                            if ($feedback_status) {
                                $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                            } else {
                                $feedback_status = new FeedbackStatus();
                                $feedback_status->vchr_status = 'New';
                                $feedback_status->vchr_color = '#000000';
                                $feedback_status->fk_int_user_id = $check_token->fk_int_user_id;
                                $feedback_status->created_by = $check_token->fk_int_user_id;
                                $feedback_status->save();
                                $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                            }

                            $enquiry->feedback_status = $fk_int_feedback_status_id;

                            $lead_types = EnquiryType::where('vchr_enquiry_type', 'woocommerce')->where('vendor_id', $vendorId)->first();
                            if ($lead_types) {
                                $fk_int_lead_type_id = $lead_types->pk_int_enquiry_type_id;
                            } else {
                                $lead_types = new EnquiryType();
                                $lead_types->vchr_enquiry_type = 'woocommerce';
                                $lead_types->vendor_id = $vendorId;
                                $lead_types->fk_int_user_id = $vendorId;
                                $lead_types->created_by = $check_token->fk_int_user_id;
                                $lead_types->save();
                                $fk_int_lead_type_id = $lead_types->pk_int_enquiry_type_id;
                            }

                            $enquiry->fk_int_enquiry_type_id = $fk_int_lead_type_id;
                            $enquiry->staff_id = $check_token->fk_int_user_id;
                            $enquiry->save();
                            try {
                                Enquiry::newLeadFunctions($enquiry->pk_int_enquiry_id);
                            } catch (\Exception $e) {
                                \Log::error($e->getMessage());
                            }

                        }
                        $order = Order::create([
                            'fk_int_user_id' => $vendorId,
                            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'name' => $enquiry->vchr_customer_name ?? $enquiry->vchr_customer_mobile,
                            'total_amount' => $request->total,
                            'status' => 0,
                            'remarks' => $request->note,
                            'staff_id' => $check_token->fk_int_user_id,
                            'created_by' => $check_token->fk_int_user_id,
                            'woocommerce_id' => $request->id,
                            'woo_order' => 1,
                            'pincode' => $request->billing['postcode'],
                            'address' => $request->billing['address_1'],
                            'bill_no' => $request->order_key,
                            'billed' => 1,
                            'payment_mode' => $request->payment_method,
                            'payment_reference' => $request->transaction_id,
                            'shipping_mode' => $request->shipping_method,
                            'awd_bill' => $request->number,
                        ]);

                        foreach ($request->line_items as $item) {

                            $product = Product::where('woocommerce_id', $item['product_id'])->first();
                            if (!$product) {
                                $slug = $this->commonObj->slugify($item['name']);
                                $product = Product::create(
                                    [
                                        'fk_int_user_id' => $check_token->fk_int_user_id,
                                        'slug' => $slug,
                                        'name' => $item['name'],
                                        'small_description' => $item['sku'],
                                        'description' => $item['name'],
                                        'stock' => 1000 ?? 0,
                                        'rate' => $item['price'],
                                        'created_by' => $check_token->fk_int_user_id,
                                        'woocommerce_id' => $request->id,
                                        'woo_product' => 1,
                                        'status' => 1
                                    ]
                                );
                            }
                            $orderItem = OrderItem::create([
                                'product_id' => $product->id,
                                'product_name' => $product->name,
                                'remarks' => "",
                                'quantity' => $item['quantity'],
                                'order_qty' => $item['quantity'],
                                'order_rate' => $item['total'],
                                'price' => $item['price'],
                                'order_id' => $order->id,
                                'status' => 0,
                                'total_amount' => $item['total']
                            ]);
                            $total_amount += $orderItem->total_amount;

                            $product->stock -= $item['quantity'];
                            $product->save();
                        }
                        // $order->total_amount = $total_amount;
                        // $order->save();

                        PaymentCollection::create([
                            'fk_int_enquiry_id' => $order->fk_int_enquiry_id,
                            'fk_int_user_id' => $check_token->fk_int_user_id,
                            'staff_id' => $check_token->fk_int_user_id,
                            'amount' => $total_amount,
                            'created_by' => $check_token->fk_int_user_id,
                            'payment_mode' => 3,
                            'order_id' => $order->id
                        ]);


                        $saleField = EnquirySalesField::firstOrCreate([
                            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
                        ], [
                            'fk_int_enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'fk_int_user_id' => $vendorId
                        ]);
                        $saleField->balance_amount = 0;
                        $saleField->save();

                        EnquiryFollowup::create([
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'note' => 'Order Placed',
                            'name' => $order->id,
                            'created_by' => $check_token->fk_int_user_id,
                            'log_type' => EnquiryFollowup::TYPE_ORDER
                        ]);


                        DB::commit();
                        // \Log::error('order commit');
                        return $this->response(200, false, 'Order Completed', new OrderResource(Order::with(['items', 'items.product', 'items.product.unit'])->findOrFail($order->id)));
                    } else {
                        return $this->response(200, true, 'Order already exist');
                    }
                } catch (\Exception $ex) {
                    DB::rollback();
                    \Log::error($ex->getMessage());
                    return $this->response(200, true, 'Failed to add Order, Please try again');
                }
            }
            return $this->response(200, true, 'Secret key not found');
        }
    }

    public function orderUpdate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];

        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Inalid Token');
                }
                if ($request->status == 'completed') {
                    $status = 1;
                } elseif ($request->status == 'cancelled' || $request->status == 'failed') {
                    $status = 2;
                } else {
                    $status = 0;
                }
                $id = $request->id;
                $order = Order::where('woocommerce_id', $id)->first();
                if ($order) {
                    $order->status = $status;
//                    $order->total_amount = $request->total;
                    $order->save();
                    $pro_id = array();
                    if ($request->line_items) {
                        foreach ($request->line_items as $item) {

                            $product = Product::where('woocommerce_id', $item['product_id'])->first();
                            $pro_id[] = $product->id;
                            $items = OrderItem::where('order_id', $order->id)->where('product_id', $product->id)->first();
                            if ($items) {
                                if ($items->quantity > $item['quantity']) {
                                    $product->stock += $items->quantity - $item['quantity'];
                                    $product->save();
                                }
                                if ($items->quantity < $item['quantity']) {
                                    $product->stock -= $item['quantity'] - $items->quantity;
                                    $product->save();
                                }


                                $items->quantity = $item['quantity'];
                                $items->order_qty = $item['quantity'];
                                $items->order_rate = $item['total'];
                                $items->price = $item['price'];
                                $items->total_amount = $item['total'];
                                $items->status = $status;
                                $items->save();
                            } else {
                                $orderItem = OrderItem::create([
                                    'product_id' => $product->id,
                                    'product_name' => $product->name,
                                    'remarks' => $item['remarks'] ?? "",
                                    'quantity' => $item['quantity'],
                                    'order_qty' => $item['quantity'],
                                    'order_rate' => $item['total'],
                                    'price' => $item['price'],
                                    'order_id' => $order->id,
                                    'status' => $status,
                                    'total_amount' => $item['total']
                                ]);

                                $product->stock -= $item['quantity'];
                                $product->save();

                            }
                            // $total_amount += $orderItem->total_amount;


                        }
                    }
                    if ($status == 1) {
                        if ($order->payment_mode == 'cod') {
                            $pay = 1;
                        } elseif ($order->payment_mode == 'cheque') {
                            $pay = 2;
                        } else {
                            $pay = 3;
                        }
                        PaymentCollection::create([
                            'fk_int_enquiry_id' => $order->fk_int_enquiry_id,
                            'fk_int_user_id' => $vendorId,
                            'staff_id' => $order->staff_id,
                            'amount' => $request->total,
                            'created_by' => $check_token->fk_int_user_id,
                            'order_id' => $order->id,
                            'payment_mode' => $pay,
                        ]);
                    }
                    $exsistIds = OrderItem::where('order_id', $order->id)->whereNotIn('product_id', $pro_id)->pluck('product_id')->toArray();
                    OrderItem::where('order_id', $order->id)->whereIn('product_id', $exsistIds)->delete();
                    // $items=OrderItem::where('order_id',$order->id)->update(['status'=>$request->status]);

                    EnquiryFollowup::create([
                        'enquiry_id' => $order->fk_int_enquiry_id,
                        'name' => $order->id,
                        'note' => 'Order (' . $order->id . ') Status Changed to Completed',
                        'log_type' => EnquiryFollowup::TYPE_ACTIVITY,

                    ]);
                    return $this->response(200, false, 'Order Completed', new OrderResource(Order::with(['items', 'items.product', 'items.product.unit'])->findOrFail($order->id)));

                }
            }
        }
    }

    public function categoryCreate(Request $request)
    {
        $request->secret = 'gl_923266bb7636684448e3';
        $validate_fields = [
            'secret' => ['required'],
        ];
        if ($request->images) {
            foreach ($request->images as $v => $image) {
                $validate_fields = [
                    'images' . $v => ['mimes:jpg,png'],
                ];
            }
        }
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
            }
            $slug = $this->commonObj->slugify($request->name);
            $parent = Category::where('woocommerce_id', $request->parent)->first();
            $category = new Category();
            $category->fk_int_user_id = $vendorId;
            $category->slug = $slug;
            $category->name = $request->name;
            $category->parent_id = ($parent) ? $parent->id : 0;
            $category->woocommerce_id = $request->id;
            $category->image = $request->image;
            $flag = $category->save();

            if ($flag)
                return $this->response(200, false, 'Category Added', new CategoryResource($category));
            else
                return $this->response(200, true, 'Failed to add category, Please try again');
        }
    }

    public function categoryUpdate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];
        if ($request->images) {
            foreach ($request->images as $v => $image) {
                $validate_fields = [
                    'images' . $v => ['mimes:jpg,png'],
                ];
            }
        }
        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            $id = $request->id;
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
            }
            $category = Category::where('fk_int_user_id', $vendorId)->where('woocommerce_id', $id)->first();
            if ($category) {
                if ($request->name) {
                    $slug = $this->commonObj->slugify($request->name);
                    // $category = Category::find($id);
                    $category->slug = $slug;
                    $category->name = $request->name;
                }
                if ($request->parent_id) {
                    $parent = Category::where('woocommerce_id', $request->parent)->first();
                    $category->parent_id = ($parent) ? $parent->id : 0;
                }
                if ($request->image) {
                    $category->image = $request->image;
                }
                $flag = $category->save();
                if ($flag)
                    return $this->response(200, false, 'Category Updated', new CategoryResource($category));
                else
                    return $this->response(200, true, 'Failed to updated category, Please try again');
            } else
                return $this->response(200, true, 'Category does not exist');
        }
    }

    public function categoryDelete(Request $request, $id)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];

        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }
                $category = Category::where('fk_int_user_id', $vendorId)->where('woocommerce_id', $id)->first();
                if ($category) {
                    $deleted = Category::where('fk_int_user_id', $vendorId)->where('woocommerce_id', $id)->delete();

                    if ($deleted)
                        return $this->response(200, false, 'Category Deleted');
                    else
                        return $this->response(200, true, 'Failed to delete category, Please try again');
                } else
                    return $this->response(200, true, 'Category does not exist');
            }
        }
    }

    public function customerCreate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];

        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            $vendorId = null;
            $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
            if ($check_token) {

                $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);

                DB::beginTransaction();
                try {
                    $enquiry = new Enquiry();
                    $enquiry->vchr_customer_name = $request->username;
                    $enquiry->vchr_customer_company_name = $request->vchr_customer_company_name;
                    $enquiry->vchr_customer_mobile = $request->country_code . $request->vchr_customer_mobile;
                    $enquiry->vchr_customer_email = $request->vchr_customer_email;
                    $enquiry->mobile_no = $request->vchr_customer_mobile;
                    $enquiry->created_by = $check_token->fk_int_user_id;
                    $enquiry->read_status = 0;

                    $feedback_status = FeedbackStatus::where('vchr_status', 'None')->where(function ($where) {
                        $where->where('fk_int_user_id', User::getVendorId())->orWhere('fk_int_user_id', 0);
                    })->first();

                    if ($feedback_status) {
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    } else {
                        $feedback_status = new FeedbackStatus();
                        $feedback_status->vchr_status = 'None';
                        $feedback_status->vchr_color = '#000000';
                        $feedback_status->fk_int_user_id = $vendorId;
                        $feedback_status->created_by = $check_token->fk_int_user_id;
                        $feedback_status->save();
                        $fk_int_feedback_status_id = $feedback_status->pk_int_feedback_status_id;
                    }

                    $enquiry->feedback_status = $request->fk_int_feedback_status_id;

                    $lead_types = EnquiryType::where('vchr_enquiry_type', 'woocommerce')->where('vendor_id', $vendorId)->first();
                    if ($lead_types) {
                        $fk_int_lead_type_id = $lead_types->pk_int_enquiry_type_id;
                    } else {
                        $lead_types = new EnquiryType();
                        $lead_types->vchr_enquiry_type = 'woocommerce';
                        $lead_types->vendor_id = $vendorId;
                        $lead_types->fk_int_user_id = $vendorId;
                        $lead_types->created_by = $check_token->fk_int_user_id;
                        $lead_types->save();
                        $fk_int_lead_type_id = $lead_types->pk_int_enquiry_type_id;
                    }

                    $enquiry->fk_int_enquiry_type_id = $fk_int_lead_type_id;
                    $enquiry->staff_id = $check_token->fk_int_user_id;
                    $enquiry->save();
                    try {
                        Enquiry::newLeadFunctions($enquiry->pk_int_enquiry_id);
                    } catch (\Exception $e) {
                        \Log::error($e->getMessage());
                    }
                    DB::commit();
                    return $this->response(200, false, 'lead Created ');
                } catch (\Exception $ex) {
                    DB::rollback();
                    \Log::error($ex->getMessage());
                    return $this->response(200, true, 'Failed to add lead, Please try again');
                }
            } else {
                return $this->response(201, true, 'Invalid Token');
            }

        }
    }

    public function customerUpdate(Request $request)
    {
        $validate_fields = [
            'secret' => ['required'],
        ];

        $validation_messages = [
        ];
        $validation_failed = $this->requestValidate($request, $validate_fields, $validation_messages);

        if ($validation_failed) {
            return $validation_failed;
        } else {
            if ($request->secret) {
                $check_token = GlApiTokens::where('vchr_token', $request->secret)->first();
                if ($check_token) {
                    $vendorId = User::getVendorIdApi($check_token->fk_int_user_id);
                    $lead = Enquiry::where('woocommerce_id', $request->id)->first();
                    if ($lead) {
                        $phone = Enquiry::where('woocommerce_id', '!=', $request->id)->where('vchr_customer_mobile', $request->billing['phone'])->first();
                        if ($phone == null) {
                            $lead->vchr_customer_email = $request->billing['email'];
                            $lead->vchr_customer_name = $request->riji;
                            $lead->vchr_customer_mobile = $request->billing['phone'];
                            $lead->mobile_no = $request->billing['phone'];
                            $lead->address = $request->billing['address_1'];
                            $lead->save();
                        }
                        return $this->response(200, false, 'lead Update ');
                    }
                } else {
                    return $this->response(201, true, 'Invalid Token');
                }

                return $this->response(200, false, 'Customer Updated');
            }
        }
    }

    public function getWooOrder()
    {
        $woo = new WooCommerceService;
        $response = $woo->GetOrder();
        return $this->response(200, false, "Orders", $response);
    }
}